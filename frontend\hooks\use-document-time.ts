'use client';

import { useState, useEffect, useRef, useCallback } from 'react';

interface DocumentTimeHook {
  isTracking: boolean;
  currentDocumentId: string | null;
  startDocumentSession: (documentId: string) => Promise<void>;
  pauseDocumentSession: (documentId?: string) => Promise<void>;
  pauseAllSessions: () => Promise<void>;
  getDocumentStats: (documentId?: string) => Promise<any>;
  error: string | null;
}

export function useDocumentTime(): DocumentTimeHook {
  const [isTracking, setIsTracking] = useState(false);
  const [currentDocumentId, setCurrentDocumentId] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const sessionIdRef = useRef<string | null>(null);

  // Get auth token from localStorage
  const getAuthToken = () => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('authToken');
    }
    return null;
  };

  // API base URL
  const API_BASE = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

  const startDocumentSession = async (documentId: string) => {
    try {
      setError(null);
      const token = getAuthToken();
      
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`${API_BASE}/api/users/document-time/start/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          document_id: documentId
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to start document session');
      }

      const data = await response.json();
      sessionIdRef.current = data.session_id;
      setCurrentDocumentId(documentId);
      setIsTracking(true);
      
      console.log(`Document session ${data.is_resumed ? 'resumed' : 'started'} for document ${documentId}`);
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Error starting document session:', err);
    }
  };

  const pauseDocumentSession = async (documentId?: string) => {
    try {
      setError(null);
      const token = getAuthToken();
      
      if (!token) {
        setIsTracking(false);
        setCurrentDocumentId(null);
        return;
      }

      const response = await fetch(`${API_BASE}/api/users/document-time/pause/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          document_id: documentId || currentDocumentId
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to pause document session');
      }

      if (documentId === currentDocumentId || !documentId) {
        sessionIdRef.current = null;
        setCurrentDocumentId(null);
        setIsTracking(false);
      }
      
      console.log(`Document session paused for document ${documentId || currentDocumentId}`);
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Error pausing document session:', err);
    }
  };

  const pauseAllSessions = async () => {
    try {
      setError(null);
      const token = getAuthToken();
      
      if (!token) {
        setIsTracking(false);
        setCurrentDocumentId(null);
        return;
      }

      const response = await fetch(`${API_BASE}/api/users/document-time/pause/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({}), // No document_id means pause all
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to pause all sessions');
      }

      sessionIdRef.current = null;
      setCurrentDocumentId(null);
      setIsTracking(false);
      
      console.log('All document sessions paused');
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Error pausing all sessions:', err);
    }
  };

  const getDocumentStats = async (documentId?: string) => {
    try {
      setError(null);
      const token = getAuthToken();
      
      if (!token) {
        throw new Error('No authentication token found');
      }

      const url = documentId 
        ? `${API_BASE}/api/users/document-time/stats/?document_id=${documentId}`
        : `${API_BASE}/api/users/document-time/stats/`;

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to get document stats');
      }

      return await response.json();
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Error getting document stats:', err);
      return null;
    }
  };

  // Handle page visibility changes to pause/resume sessions
  const handleVisibilityChange = useCallback(() => {
    if (document.hidden && isTracking && currentDocumentId) {
      // Page is hidden, pause current session
      pauseDocumentSession(currentDocumentId);
    }
  }, [isTracking, currentDocumentId]);

  // Handle beforeunload to pause sessions when leaving page
  const handleBeforeUnload = useCallback(() => {
    if (isTracking && currentDocumentId) {
      // Use navigator.sendBeacon for reliable cleanup
      const token = getAuthToken();
      if (token) {
        const data = JSON.stringify({ document_id: currentDocumentId });
        navigator.sendBeacon(
          `${API_BASE}/api/users/document-time/pause/`,
          new Blob([data], { type: 'application/json' })
        );
      }
    }
  }, [isTracking, currentDocumentId]);

  // Setup event listeners
  useEffect(() => {
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [handleVisibilityChange, handleBeforeUnload]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (isTracking && currentDocumentId) {
        pauseDocumentSession(currentDocumentId);
      }
    };
  }, []);

  return {
    isTracking,
    currentDocumentId,
    startDocumentSession,
    pauseDocumentSession,
    pauseAllSessions,
    getDocumentStats,
    error,
  };
}
