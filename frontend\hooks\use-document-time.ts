'use client'

import { useEffect, useRef, useCallback } from 'react'
import { useRouter, usePathname } from 'next/navigation'

interface UseDocumentTimeProps {
  documentId?: number
  enabled?: boolean
  isProcessingComplete?: boolean  // New prop to control when tracking starts
  onQuizStart?: () => void
  onQuizEnd?: () => void
}

export function useDocumentTime({
  documentId,
  enabled = true,
  isProcessingComplete = false,  // Only start tracking after processing is complete
  onQuizStart,
  onQuizEnd
}: UseDocumentTimeProps = {}) {
  const sessionIdRef = useRef<number | null>(null)
  const isActiveRef = useRef(false)
  const isPausedRef = useRef(false)
  const currentDocumentRef = useRef<number | undefined>(documentId)
  const sessionStartTimeRef = useRef<Date | null>(null)
  const isStartingSessionRef = useRef(false)
  const lastSessionDocumentRef = useRef<number | undefined>(undefined)
  const router = useRouter()
  const pathname = usePathname()

  // Get the correct API base URL
  const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api'

  const getAuthHeaders = () => {
    const token = localStorage.getItem('token')
    return {
      'Content-Type': 'application/json',
      'Authorization': `Token ${token}`,
    }
  }

  const startSession = useCallback(async () => {
    if (!enabled || !documentId || !isProcessingComplete || isActiveRef.current || isStartingSessionRef.current) return

    // Prevent duplicate sessions for the same document
    if (lastSessionDocumentRef.current === documentId && isActiveRef.current) {
      console.log('Session already active for document:', documentId)
      return
    }

    isStartingSessionRef.current = true

    try {
      const token = localStorage.getItem('token')
      if (!token) {
        isStartingSessionRef.current = false
        return
      }

      console.log('Starting new session for document:', documentId)
      const response = await fetch(`${API_BASE_URL}/users/platform-time/start/`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify({ document_id: documentId }),
      })

      if (response.ok) {
        const data = await response.json()
        sessionIdRef.current = data.session_id
        isActiveRef.current = true
        isPausedRef.current = false
        currentDocumentRef.current = documentId
        lastSessionDocumentRef.current = documentId
        sessionStartTimeRef.current = new Date()
        console.log('Document time session started successfully:', data.session_id, data.message)
      } else {
        console.error('Failed to start session:', response.status, response.statusText)
      }
    } catch (error) {
      console.error('Error starting document time session:', error)
    } finally {
      isStartingSessionRef.current = false
    }
  }, [documentId, enabled, isProcessingComplete])

  const pauseSession = useCallback(async (reason: 'quiz' | 'navigation' | 'manual' = 'manual') => {
    if (!documentId || !isActiveRef.current || isPausedRef.current) return

    try {
      const token = localStorage.getItem('token')
      if (!token) return

      const response = await fetch(`${API_BASE_URL}/users/platform-time/pause/`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify({
          document_id: documentId,
          reason: reason
        }),
      })

      if (response.ok) {
        isPausedRef.current = true
        console.log(`Document time session paused: ${reason}`)
      }
    } catch (error) {
      console.error('Error pausing document time session:', error)
    }
  }, [documentId])

  const resumeSession = useCallback(async () => {
    if (!documentId || !isActiveRef.current || !isPausedRef.current) return

    try {
      const token = localStorage.getItem('token')
      if (!token) return

      const response = await fetch(`${API_BASE_URL}/users/platform-time/resume/`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify({ document_id: documentId }),
      })

      if (response.ok) {
        isPausedRef.current = false
        console.log('Document time session resumed')
      }
    } catch (error) {
      console.error('Error resuming document time session:', error)
    }
  }, [documentId])

  const endSession = useCallback(async () => {
    if (!documentId || !isActiveRef.current) return

    try {
      const token = localStorage.getItem('token')
      if (!token) return

      console.log('Ending session for document:', documentId, 'sessionId:', sessionIdRef.current)

      // Use session_id if available, otherwise fall back to document_id
      const requestBody = sessionIdRef.current
        ? { session_id: sessionIdRef.current }
        : { document_id: documentId }

      const response = await fetch(`${API_BASE_URL}/users/platform-time/end/`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify(requestBody),
      })

      if (response.ok) {
        const data = await response.json()
        console.log('Document time session ended successfully:', data)

        // Clear all session state
        sessionIdRef.current = null
        isActiveRef.current = false
        isPausedRef.current = false
        sessionStartTimeRef.current = null
        isStartingSessionRef.current = false
        lastSessionDocumentRef.current = undefined
      } else {
        console.error('Failed to end session:', response.status, await response.text())
      }
    } catch (error) {
      console.error('Error ending document time session:', error)
    }
  }, [documentId])

  // Quiz control functions
  const startQuiz = useCallback(() => {
    pauseSession('quiz')
    onQuizStart?.()
  }, [pauseSession, onQuizStart])

  const endQuiz = useCallback(() => {
    resumeSession()
    onQuizEnd?.()
  }, [resumeSession, onQuizEnd])

  // Effect to handle document changes and session lifecycle
  useEffect(() => {
    if (!enabled || !documentId || !isProcessingComplete) return

    // If document changed, end previous session first
    if (currentDocumentRef.current !== documentId && isActiveRef.current) {
      console.log('Document changed from', currentDocumentRef.current, 'to', documentId, '- ending previous session')
      endSession()
    }

    // Update current document reference
    currentDocumentRef.current = documentId

    // Only start session if not already active AND not currently starting
    if (!isActiveRef.current && !isStartingSessionRef.current) {
      console.log('Attempting to start session for document:', documentId, 'isActive:', isActiveRef.current, 'isStarting:', isStartingSessionRef.current, 'pathname:', pathname)
      startSession()
    }
  }, [documentId, enabled, isProcessingComplete, pathname, startSession, endSession])

  // Separate effect to handle navigation away from process page
  useEffect(() => {
    console.log('Pathname changed to:', pathname, 'isActive:', isActiveRef.current)
    if (pathname !== '/process' && isActiveRef.current) {
      console.log('Navigation detected - ending session due to pathname change:', pathname)
      endSession()
    }
  }, [pathname, endSession])

  // Effect to handle page unload
  useEffect(() => {
    const handleBeforeUnload = () => {
      if (isActiveRef.current) {
        endSession()
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
    }
  }, [endSession])

  return {
    sessionId: sessionIdRef.current,
    isActive: isActiveRef.current,
    isPaused: isPausedRef.current,
    sessionStartTime: sessionStartTimeRef.current,
    startQuiz,
    endQuiz,
    pauseSession,
    resumeSession,
    endSession,
  }
}
