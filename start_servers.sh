#!/bin/bash

echo "Activating virtual environment and starting servers..."

# Activate the virtual environment
source venv/bin/activate

# Start Django server in background
echo "Starting Django server on port 8000..."
(cd core && python manage.py runserver) &

# Start FastAPI server in background
echo "Starting FastAPI server on port 8001..."
(cd llm_api && uvicorn main:app --reload --host 0.0.0.0 --port 8001) &

echo "Both servers are starting. You can now run the frontend with 'npm run dev' in the frontend directory."
