from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from .models import (
    Student, UserUsage, StudentPerformance, UserProfile,
    PlatformTimeSession, PlatformTimeStats
)
from chat.models import ChatSession, ChatMessage

# Register your models here.
admin.site.register(Student)
admin.site.register(UserUsage)
admin.site.register(UserProfile)
admin.site.register(StudentPerformance)
admin.site.register(ChatSession)
admin.site.register(ChatMessage)

# Simple platform time tracking models
@admin.register(PlatformTimeSession)
class PlatformTimeSessionAdmin(admin.ModelAdmin):
    list_display = ['student', 'document', 'total_time_seconds', 'session_start', 'session_end', 'last_activity']
    list_filter = ['session_start', 'session_end']
    search_fields = ['student__username', 'student__email', 'document__title']
    readonly_fields = ['created_at', 'last_activity']
    ordering = ['-session_start']

@admin.register(PlatformTimeStats)
class PlatformTimeStatsAdmin(admin.ModelAdmin):
    list_display = ['student', 'document', 'total_time_seconds', 'total_sessions', 'last_access']
    list_filter = ['last_access', 'first_access']
    search_fields = ['student__username', 'student__email', 'document__title']
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['-last_access']
