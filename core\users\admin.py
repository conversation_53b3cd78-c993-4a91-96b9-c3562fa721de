from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from .models import (
    Student, UserUsage, StudentPerformance, UserProfile,
    DocumentTimeSession, DocumentTimeStats
)
from chat.models import ChatSession, ChatMessage

# Register your models here.
admin.site.register(Student)
admin.site.register(UserUsage)
admin.site.register(UserProfile)
admin.site.register(StudentPerformance)
admin.site.register(ChatSession)
admin.site.register(ChatMessage)

# Document-specific time tracking models
@admin.register(DocumentTimeSession)
class DocumentTimeSessionAdmin(admin.ModelAdmin):
    list_display = ['student', 'document', 'total_time_seconds', 'is_active', 'session_start', 'session_end']
    list_filter = ['session_start', 'session_end', 'is_active']
    search_fields = ['student__username', 'student__email', 'document__title']
    readonly_fields = ['created_at']
    ordering = ['-session_start']

@admin.register(DocumentTimeStats)
class DocumentTimeStatsAdmin(admin.ModelAdmin):
    list_display = ['student', 'document', 'total_time_seconds', 'total_sessions', 'view_count', 'was_reopened', 'last_access']
    list_filter = ['last_access', 'first_access', 'view_count']
    search_fields = ['student__username', 'student__email', 'document__title']
    readonly_fields = ['created_at', 'updated_at', 'average_session_time', 'was_reopened']
    ordering = ['-total_time_seconds']
