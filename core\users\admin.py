from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from .models import (
    Student, UserUsage, StudentPerformance, UserProfile,
    DocumentTimeSession, DocumentTimeInterval, DocumentTimeStats
)
from chat.models import ChatSession, ChatMessage

# Register your models here.
admin.site.register(Student)
admin.site.register(UserUsage)
admin.site.register(UserProfile)
admin.site.register(StudentPerformance)
admin.site.register(ChatSession)
admin.site.register(ChatMessage)

# Time tracking models
@admin.register(DocumentTimeSession)
class DocumentTimeSessionAdmin(admin.ModelAdmin):
    list_display = ['student', 'document', 'status', 'total_time_formatted', 'session_start', 'last_activity']
    list_filter = ['status', 'session_start', 'last_activity']
    search_fields = ['student__username', 'student__email', 'document__title']
    readonly_fields = ['total_time_formatted', 'created_at', 'updated_at']
    ordering = ['-session_start']

@admin.register(DocumentTimeInterval)
class DocumentTimeIntervalAdmin(admin.ModelAdmin):
    list_display = ['session', 'interval_type', 'duration_formatted', 'start_time', 'end_time']
    list_filter = ['interval_type', 'start_time']
    search_fields = ['session__student__username', 'session__document__title']
    readonly_fields = ['duration_formatted', 'created_at']
    ordering = ['-start_time']

@admin.register(DocumentTimeStats)
class DocumentTimeStatsAdmin(admin.ModelAdmin):
    list_display = ['student', 'document', 'total_study_time_formatted', 'total_sessions', 'view_count', 'reopened_at_least_once', 'last_access']
    list_filter = ['reopened_at_least_once', 'last_access', 'first_access']
    search_fields = ['student__username', 'student__email', 'document__title']
    readonly_fields = ['total_study_time_formatted', 'average_session_duration_formatted', 'created_at', 'updated_at']
    ordering = ['-last_access']
