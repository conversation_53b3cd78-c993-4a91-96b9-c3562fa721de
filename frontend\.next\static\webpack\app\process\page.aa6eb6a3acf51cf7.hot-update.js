"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/process/page",{

/***/ "(app-pages-browser)/./hooks/use-document-time.ts":
/*!************************************!*\
  !*** ./hooks/use-document-time.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDocumentTime: () => (/* binding */ useDocumentTime)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ useDocumentTime auto */ \n\nfunction useDocumentTime() {\n    let { documentId, enabled = true, isProcessingComplete = false, onQuizStart, onQuizEnd } = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    const sessionIdRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const isActiveRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const isPausedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const currentDocumentRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(documentId);\n    const sessionStartTimeRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const isStartingSessionRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const lastSessionDocumentRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(undefined);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    // Get the correct API base URL\n    const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';\n    const getAuthHeaders = ()=>{\n        const token = localStorage.getItem('token');\n        return {\n            'Content-Type': 'application/json',\n            'Authorization': \"Token \".concat(token)\n        };\n    };\n    const startSession = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDocumentTime.useCallback[startSession]\": async ()=>{\n            if (!enabled || !documentId || !isProcessingComplete || isActiveRef.current || isStartingSessionRef.current) return;\n            // Prevent duplicate sessions for the same document\n            if (lastSessionDocumentRef.current === documentId && isActiveRef.current) {\n                console.log('Session already exists for document:', documentId);\n                return;\n            }\n            try {\n                isStartingSessionRef.current = true;\n                const token = localStorage.getItem('token');\n                if (!token) {\n                    isStartingSessionRef.current = false;\n                    return;\n                }\n                console.log('Starting session for document:', documentId);\n                const response = await fetch(\"\".concat(API_BASE_URL, \"/users/platform-time/start/\"), {\n                    method: 'POST',\n                    headers: getAuthHeaders(),\n                    body: JSON.stringify({\n                        document_id: documentId\n                    })\n                });\n                if (response.ok) {\n                    const data = await response.json();\n                    sessionIdRef.current = data.session_id;\n                    isActiveRef.current = true;\n                    isPausedRef.current = false;\n                    currentDocumentRef.current = documentId;\n                    lastSessionDocumentRef.current = documentId;\n                    sessionStartTimeRef.current = new Date() // Record when session started\n                    ;\n                    console.log('Document time session started:', data.session_id, data.message);\n                } else {\n                    console.error('Failed to start session:', response.status, response.statusText);\n                }\n            } catch (error) {\n                console.error('Error starting document time session:', error);\n            } finally{\n                isStartingSessionRef.current = false;\n            }\n        }\n    }[\"useDocumentTime.useCallback[startSession]\"], [\n        documentId,\n        enabled,\n        isProcessingComplete\n    ]);\n    const pauseSession = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDocumentTime.useCallback[pauseSession]\": async function() {\n            let reason = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'manual';\n            if (!documentId || !isActiveRef.current || isPausedRef.current) return;\n            try {\n                const token = localStorage.getItem('token');\n                if (!token) return;\n                const response = await fetch(\"\".concat(API_BASE_URL, \"/users/platform-time/pause/\"), {\n                    method: 'POST',\n                    headers: getAuthHeaders(),\n                    body: JSON.stringify({\n                        document_id: documentId,\n                        reason: reason\n                    })\n                });\n                if (response.ok) {\n                    isPausedRef.current = true;\n                    console.log(\"Document time session paused: \".concat(reason));\n                }\n            } catch (error) {\n                console.error('Error pausing document time session:', error);\n            }\n        }\n    }[\"useDocumentTime.useCallback[pauseSession]\"], [\n        documentId\n    ]);\n    const resumeSession = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDocumentTime.useCallback[resumeSession]\": async ()=>{\n            if (!documentId || !isActiveRef.current || !isPausedRef.current) return;\n            try {\n                const token = localStorage.getItem('token');\n                if (!token) return;\n                const response = await fetch(\"\".concat(API_BASE_URL, \"/users/platform-time/resume/\"), {\n                    method: 'POST',\n                    headers: getAuthHeaders(),\n                    body: JSON.stringify({\n                        document_id: documentId\n                    })\n                });\n                if (response.ok) {\n                    isPausedRef.current = false;\n                    console.log('Document time session resumed');\n                }\n            } catch (error) {\n                console.error('Error resuming document time session:', error);\n            }\n        }\n    }[\"useDocumentTime.useCallback[resumeSession]\"], [\n        documentId\n    ]);\n    const endSession = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDocumentTime.useCallback[endSession]\": async ()=>{\n            if (!documentId || !isActiveRef.current) return;\n            try {\n                const token = localStorage.getItem('token');\n                if (!token) return;\n                await fetch(\"\".concat(API_BASE_URL, \"/users/platform-time/end/\"), {\n                    method: 'POST',\n                    headers: getAuthHeaders(),\n                    body: JSON.stringify({\n                        document_id: documentId\n                    })\n                });\n                sessionIdRef.current = null;\n                isActiveRef.current = false;\n                isPausedRef.current = false;\n                sessionStartTimeRef.current = null;\n                isStartingSessionRef.current = false;\n                lastSessionDocumentRef.current = undefined;\n                console.log('Document time session ended');\n            } catch (error) {\n                console.error('Error ending document time session:', error);\n            }\n        }\n    }[\"useDocumentTime.useCallback[endSession]\"], [\n        documentId\n    ]);\n    // Quiz control functions\n    const startQuiz = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDocumentTime.useCallback[startQuiz]\": ()=>{\n            pauseSession('quiz');\n            onQuizStart === null || onQuizStart === void 0 ? void 0 : onQuizStart();\n        }\n    }[\"useDocumentTime.useCallback[startQuiz]\"], [\n        pauseSession,\n        onQuizStart\n    ]);\n    const endQuiz = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDocumentTime.useCallback[endQuiz]\": ()=>{\n            resumeSession();\n            onQuizEnd === null || onQuizEnd === void 0 ? void 0 : onQuizEnd();\n        }\n    }[\"useDocumentTime.useCallback[endQuiz]\"], [\n        resumeSession,\n        onQuizEnd\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useDocumentTime.useEffect\": ()=>{\n            if (!enabled || !documentId || !isProcessingComplete) return;\n            // If document changed, end previous session and start new one\n            if (currentDocumentRef.current !== documentId) {\n                if (isActiveRef.current) {\n                    endSession();\n                }\n                currentDocumentRef.current = documentId;\n            }\n            // Only start session if not already active AND not currently starting\n            if (!isActiveRef.current && !isStartingSessionRef.current) {\n                startSession();\n            }\n            // Handle page unload - end session when user leaves\n            const handleBeforeUnload = {\n                \"useDocumentTime.useEffect.handleBeforeUnload\": ()=>{\n                    if (isActiveRef.current) {\n                        endSession();\n                    }\n                }\n            }[\"useDocumentTime.useEffect.handleBeforeUnload\"];\n            // Handle navigation away from process page - end session\n            const handleRouteChange = {\n                \"useDocumentTime.useEffect.handleRouteChange\": ()=>{\n                    if (window.location.pathname !== '/process' && isActiveRef.current) {\n                        endSession();\n                    }\n                }\n            }[\"useDocumentTime.useEffect.handleRouteChange\"];\n            window.addEventListener('beforeunload', handleBeforeUnload);\n            window.addEventListener('popstate', handleRouteChange);\n            return ({\n                \"useDocumentTime.useEffect\": ()=>{\n                    window.removeEventListener('beforeunload', handleBeforeUnload);\n                    window.removeEventListener('popstate', handleRouteChange);\n                }\n            })[\"useDocumentTime.useEffect\"];\n        }\n    }[\"useDocumentTime.useEffect\"], [\n        documentId,\n        enabled,\n        isProcessingComplete\n    ]);\n    return {\n        sessionId: sessionIdRef.current,\n        isActive: isActiveRef.current,\n        isPaused: isPausedRef.current,\n        sessionStartTime: sessionStartTimeRef.current,\n        startQuiz,\n        endQuiz,\n        pauseSession,\n        resumeSession,\n        endSession\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./hooks/use-document-time.ts\n"));

/***/ })

});