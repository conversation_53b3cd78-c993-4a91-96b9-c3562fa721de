"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/process/page",{

/***/ "(app-pages-browser)/./hooks/use-document-time.ts":
/*!************************************!*\
  !*** ./hooks/use-document-time.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDocumentTime: () => (/* binding */ useDocumentTime)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ useDocumentTime auto */ \n\nfunction useDocumentTime() {\n    let { documentId, enabled = true, isProcessingComplete = false, onQuizStart, onQuizEnd } = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    const sessionIdRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const isActiveRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const isPausedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const currentDocumentRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(documentId);\n    const sessionStartTimeRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const isStartingSessionRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const lastSessionDocumentRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(undefined);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    // Get the correct API base URL\n    const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';\n    const getAuthHeaders = ()=>{\n        const token = localStorage.getItem('token');\n        return {\n            'Content-Type': 'application/json',\n            'Authorization': \"Token \".concat(token)\n        };\n    };\n    const startSession = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDocumentTime.useCallback[startSession]\": async ()=>{\n            if (!enabled || !documentId || !isProcessingComplete || isActiveRef.current || isStartingSessionRef.current) return;\n            // Prevent duplicate sessions for the same document\n            if (lastSessionDocumentRef.current === documentId && isActiveRef.current) {\n                console.log('Session already exists for document:', documentId);\n                return;\n            }\n            try {\n                isStartingSessionRef.current = true;\n                const token = localStorage.getItem('token');\n                if (!token) {\n                    isStartingSessionRef.current = false;\n                    return;\n                }\n                console.log('Starting session for document:', documentId);\n                const response = await fetch(\"\".concat(API_BASE_URL, \"/users/platform-time/start/\"), {\n                    method: 'POST',\n                    headers: getAuthHeaders(),\n                    body: JSON.stringify({\n                        document_id: documentId\n                    })\n                });\n                if (response.ok) {\n                    const data = await response.json();\n                    sessionIdRef.current = data.session_id;\n                    isActiveRef.current = true;\n                    isPausedRef.current = false;\n                    currentDocumentRef.current = documentId;\n                    lastSessionDocumentRef.current = documentId;\n                    sessionStartTimeRef.current = new Date() // Record when session started\n                    ;\n                    console.log('Document time session started:', data.session_id, data.message);\n                } else {\n                    console.error('Failed to start session:', response.status, response.statusText);\n                }\n            } catch (error) {\n                console.error('Error starting document time session:', error);\n            } finally{\n                isStartingSessionRef.current = false;\n            }\n        }\n    }[\"useDocumentTime.useCallback[startSession]\"], [\n        documentId,\n        enabled,\n        isProcessingComplete\n    ]);\n    const pauseSession = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDocumentTime.useCallback[pauseSession]\": async function() {\n            let reason = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'manual';\n            if (!documentId || !isActiveRef.current || isPausedRef.current) return;\n            try {\n                const token = localStorage.getItem('token');\n                if (!token) return;\n                const response = await fetch(\"\".concat(API_BASE_URL, \"/users/platform-time/pause/\"), {\n                    method: 'POST',\n                    headers: getAuthHeaders(),\n                    body: JSON.stringify({\n                        document_id: documentId,\n                        reason: reason\n                    })\n                });\n                if (response.ok) {\n                    isPausedRef.current = true;\n                    console.log(\"Document time session paused: \".concat(reason));\n                }\n            } catch (error) {\n                console.error('Error pausing document time session:', error);\n            }\n        }\n    }[\"useDocumentTime.useCallback[pauseSession]\"], [\n        documentId\n    ]);\n    const resumeSession = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDocumentTime.useCallback[resumeSession]\": async ()=>{\n            if (!documentId || !isActiveRef.current || !isPausedRef.current) return;\n            try {\n                const token = localStorage.getItem('token');\n                if (!token) return;\n                const response = await fetch(\"\".concat(API_BASE_URL, \"/users/platform-time/resume/\"), {\n                    method: 'POST',\n                    headers: getAuthHeaders(),\n                    body: JSON.stringify({\n                        document_id: documentId\n                    })\n                });\n                if (response.ok) {\n                    isPausedRef.current = false;\n                    console.log('Document time session resumed');\n                }\n            } catch (error) {\n                console.error('Error resuming document time session:', error);\n            }\n        }\n    }[\"useDocumentTime.useCallback[resumeSession]\"], [\n        documentId\n    ]);\n    const endSession = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDocumentTime.useCallback[endSession]\": async ()=>{\n            if (!documentId || !isActiveRef.current) return;\n            try {\n                const token = localStorage.getItem('token');\n                if (!token) return;\n                await fetch(\"\".concat(API_BASE_URL, \"/users/platform-time/end/\"), {\n                    method: 'POST',\n                    headers: getAuthHeaders(),\n                    body: JSON.stringify({\n                        document_id: documentId\n                    })\n                });\n                sessionIdRef.current = null;\n                isActiveRef.current = false;\n                isPausedRef.current = false;\n                sessionStartTimeRef.current = null;\n                isStartingSessionRef.current = false;\n                lastSessionDocumentRef.current = undefined;\n                console.log('Document time session ended');\n            } catch (error) {\n                console.error('Error ending document time session:', error);\n            }\n        }\n    }[\"useDocumentTime.useCallback[endSession]\"], [\n        documentId\n    ]);\n    // Quiz control functions\n    const startQuiz = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDocumentTime.useCallback[startQuiz]\": ()=>{\n            pauseSession('quiz');\n            onQuizStart === null || onQuizStart === void 0 ? void 0 : onQuizStart();\n        }\n    }[\"useDocumentTime.useCallback[startQuiz]\"], [\n        pauseSession,\n        onQuizStart\n    ]);\n    const endQuiz = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDocumentTime.useCallback[endQuiz]\": ()=>{\n            resumeSession();\n            onQuizEnd === null || onQuizEnd === void 0 ? void 0 : onQuizEnd();\n        }\n    }[\"useDocumentTime.useCallback[endQuiz]\"], [\n        resumeSession,\n        onQuizEnd\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useDocumentTime.useEffect\": ()=>{\n            if (!enabled || !documentId || !isProcessingComplete) return;\n            // If document changed, end previous session and start new one\n            if (currentDocumentRef.current !== documentId) {\n                if (isActiveRef.current) {\n                    endSession();\n                }\n                currentDocumentRef.current = documentId;\n                lastSessionDocumentRef.current = undefined // Reset for new document\n                ;\n            }\n            // Only start session if not already active AND not currently starting\n            if (!isActiveRef.current && !isStartingSessionRef.current) {\n                startSession();\n            }\n            // Handle page unload - end session when user leaves\n            const handleBeforeUnload = {\n                \"useDocumentTime.useEffect.handleBeforeUnload\": ()=>{\n                    if (isActiveRef.current) {\n                        endSession();\n                    }\n                }\n            }[\"useDocumentTime.useEffect.handleBeforeUnload\"];\n            // Handle navigation away from process page - end session\n            const handleRouteChange = {\n                \"useDocumentTime.useEffect.handleRouteChange\": ()=>{\n                    if (window.location.pathname !== '/process' && isActiveRef.current) {\n                        endSession();\n                    }\n                }\n            }[\"useDocumentTime.useEffect.handleRouteChange\"];\n            window.addEventListener('beforeunload', handleBeforeUnload);\n            window.addEventListener('popstate', handleRouteChange);\n            return ({\n                \"useDocumentTime.useEffect\": ()=>{\n                    window.removeEventListener('beforeunload', handleBeforeUnload);\n                    window.removeEventListener('popstate', handleRouteChange);\n                }\n            })[\"useDocumentTime.useEffect\"];\n        }\n    }[\"useDocumentTime.useEffect\"], [\n        documentId,\n        enabled,\n        isProcessingComplete\n    ]);\n    return {\n        sessionId: sessionIdRef.current,\n        isActive: isActiveRef.current,\n        isPaused: isPausedRef.current,\n        sessionStartTime: sessionStartTimeRef.current,\n        startQuiz,\n        endQuiz,\n        pauseSession,\n        resumeSession,\n        endSession\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./hooks/use-document-time.ts\n"));

/***/ })

});