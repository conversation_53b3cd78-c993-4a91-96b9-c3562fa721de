from django.contrib.auth.models import AbstractUser, Group, Permission
from django.db import models
from django.utils import timezone
from django.core.cache import cache
from datetime import timedelta
from django.db.models.signals import post_save
from django.dispatch import receiver
import pytz

class Student(AbstractUser):
    is_paid = models.BooleanField(default=False)

    email = models.EmailField(unique=True)
    is_email_verified = models.BooleanField(default=False)
    email_verification_token = models.CharField(max_length=100, null=True, blank=True)
    email_verification_sent_at = models.DateTimeField(null=True, blank=True)
    otp = models.CharField(max_length=6, null=True, blank=True)
    otp_created_at = models.DateTimeField(null=True, blank=True)

    USERNAME_FIELD = 'email'  # Login using email instead of username
    REQUIRED_FIELDS = ['username']  # username is still required in form

    groups = models.ManyToManyField(
        Group,
        related_name='student_users',
        blank=True,
        help_text='The groups this user belongs to.',
        verbose_name='groups'
    )
    user_permissions = models.ManyToManyField(
        Permission,
        related_name='student_users',
        blank=True,
        help_text='Specific permissions for this user.',
        verbose_name='user permissions'
    )

    def __str__(self):
        return self.email

    def generate_verification_token(self):
        import secrets
        self.email_verification_token = secrets.token_urlsafe(32)
        self.email_verification_sent_at = timezone.now()
        self.save()
        return self.email_verification_token

    def generate_otp(self):
        import random
        import string
        self.otp = ''.join(random.choices(string.digits, k=6))
        self.otp_created_at = timezone.now()
        self.save()
        return self.otp

    def verify_otp(self, otp):
        if not self.otp or not self.otp_created_at:
            return False

        # Check if OTP is expired (10 minutes validity)
        if timezone.now() > self.otp_created_at + timezone.timedelta(minutes=10):
            return False

        return self.otp == otp

@receiver(post_save, sender=Student)
def create_user_profile(sender, instance, created, **kwargs):
    if created:
        UserProfile.objects.create(user=instance)

@receiver(post_save, sender=Student)
def save_user_profile(sender, instance, **kwargs):
    if not hasattr(instance, 'userprofile'):
        UserProfile.objects.create(user=instance)
    instance.userprofile.save()

class UserProfile(models.Model):
    user = models.OneToOneField('Student', on_delete=models.CASCADE)
    is_paid = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username} - {'Paid' if self.is_paid else 'Free'}"

class UserUsage(models.Model):
    user = models.ForeignKey('Student', on_delete=models.CASCADE)
    date = models.DateField(default=timezone.now)
    chat_count = models.IntegerField(default=0)
    file_upload_count = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('user', 'date')
        indexes = [
            models.Index(fields=['user', 'date']),
            models.Index(fields=['date']),  # For efficient cleanup
        ]

    def __str__(self):
        return f"{self.user.username} - {self.date} - Chats: {self.chat_count}, Files: {self.file_upload_count}"

    @property
    def chat_limit(self):
        return 100 if self.user.userprofile.is_paid else 5000

    @property
    def file_upload_limit(self):
        return 5 if self.user.userprofile.is_paid else 100

    def can_make_chat(self):
        return self.chat_count < self.chat_limit

    def can_upload_file(self):
        return self.file_upload_count < self.file_upload_limit

    def increment_chat_count(self):
        self.chat_count = self.chat_count + 1
        UserUsage.objects.filter(id=self.id).update(chat_count=self.chat_count)
        self._update_cache()

    def increment_file_upload_count(self):
        self.file_upload_count = self.file_upload_count + 1
        UserUsage.objects.filter(id=self.id).update(file_upload_count=self.file_upload_count)
        self._update_cache()

    def _update_cache(self):
        """Update cache with current usage data"""
        cache_key = f"user_usage_{self.user.id}_{self.date}"
        cache.set(cache_key, {
            'chat_count': self.chat_count,
            'file_upload_count': self.file_upload_count,
            'chat_limit': self.chat_limit,
            'file_upload_limit': self.file_upload_limit
        }, timeout=86400)  # Cache for 24 hours

    @classmethod
    def get_or_create_usage(cls, user, date=None):
        """Get or create usage record with caching"""
        if date is None:
            date = timezone.now().date()

        cache_key = f"user_usage_{user.id}_{date}"
        cached_data = cache.get(cache_key)

        if cached_data:
            usage, _ = cls.objects.get_or_create(
                user=user,
                date=date,
                defaults={
                    'chat_count': cached_data['chat_count'],
                    'file_upload_count': cached_data['file_upload_count']
                }
            )
            return usage

        usage, created = cls.objects.get_or_create(
            user=user,
            date=date,
            defaults={'chat_count': 0, 'file_upload_count': 0}
        )

        if created:
            usage._update_cache()

        return usage

    @classmethod
    def cleanup_old_records(cls, days_to_keep=30):
        """Efficient cleanup of old records using bulk operations"""
        cutoff_date = timezone.now().date() - timedelta(days=days_to_keep)

        # Get IDs of records to delete
        old_record_ids = list(cls.objects.filter(date__lt=cutoff_date).values_list('id', flat=True))

        if old_record_ids:
            # Delete in chunks to avoid memory issues
            chunk_size = 1000
            for i in range(0, len(old_record_ids), chunk_size):
                chunk = old_record_ids[i:i + chunk_size]
                cls.objects.filter(id__in=chunk).delete()

        return len(old_record_ids)


class StudentPerformance(models.Model):
    """
    Model to track student performance on quizzes for specific documents.
    Each entry represents a single quiz attempt with score and time taken.
    """
    student = models.ForeignKey('Student', on_delete=models.CASCADE, related_name='performances')
    document = models.ForeignKey('documents.Document', on_delete=models.CASCADE, related_name='student_performances')
    quiz_score = models.DecimalField(max_digits=5, decimal_places=2, default=0.0,
                                    help_text="Score achieved on the quiz (percentage)")
    time_taken = models.PositiveIntegerField(help_text="Time taken to complete the quiz (in seconds)")
    remarks = models.TextField(blank=True, null=True, help_text="Feedback or comments on student performance")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        indexes = [
            models.Index(fields=['student', 'document']),
            models.Index(fields=['quiz_score']),  # For performance analytics
            models.Index(fields=['created_at']),  # For chronological ordering
        ]
        ordering = ['-created_at']
        verbose_name = "Student Performance"
        verbose_name_plural = "Student Performances"

    def __str__(self):
        return f"{self.student.username} - {self.document.title} - Score: {self.quiz_score}% - {self.created_at.strftime('%Y-%m-%d %H:%M')}"


class DocumentTimeSession(models.Model):
    """
    Model to track time spent by users on documents.
    Each session represents a continuous period of document interaction.
    """
    SESSION_STATUS = [
        ('active', 'Active'),
        ('paused', 'Paused'),
        ('completed', 'Completed'),
        ('abandoned', 'Abandoned')
    ]

    student = models.ForeignKey('Student', on_delete=models.CASCADE, related_name='document_sessions')
    document = models.ForeignKey('documents.Document', on_delete=models.CASCADE, related_name='time_sessions')
    session_start = models.DateTimeField(help_text="When the session started (Indian timezone)")
    session_end = models.DateTimeField(null=True, blank=True, help_text="When the session ended (Indian timezone)")
    total_time_seconds = models.PositiveIntegerField(default=0, help_text="Total time spent in seconds")
    status = models.CharField(max_length=20, choices=SESSION_STATUS, default='active')
    last_activity = models.DateTimeField(auto_now=True, help_text="Last recorded activity (for heartbeat tracking)")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=['student', 'document']),
            models.Index(fields=['status']),
            models.Index(fields=['session_start']),
            models.Index(fields=['last_activity']),
        ]
        ordering = ['-session_start']
        verbose_name = "Document Time Session"
        verbose_name_plural = "Document Time Sessions"

    def __str__(self):
        return f"{self.student.username} - {self.document.title} - {self.get_status_display()} - {self.total_time_seconds}s"

    @property
    def total_time_formatted(self):
        """Return total time in HH:MM:SS format"""
        hours = self.total_time_seconds // 3600
        minutes = (self.total_time_seconds % 3600) // 60
        seconds = self.total_time_seconds % 60
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"

    def get_indian_timezone(self):
        """Get Indian timezone object"""
        return pytz.timezone('Asia/Kolkata')

    def start_session(self):
        """Start or resume the session"""
        indian_tz = self.get_indian_timezone()
        if not self.session_start:
            self.session_start = timezone.now().astimezone(indian_tz)
        self.status = 'active'
        self.last_activity = timezone.now()
        self.save()

    def pause_session(self):
        """Pause the session (e.g., when starting a quiz)"""
        if self.status == 'active':
            self.status = 'paused'
            self.last_activity = timezone.now()
            self.save()

    def resume_session(self):
        """Resume the session (e.g., after completing a quiz)"""
        if self.status == 'paused':
            self.status = 'active'
            self.last_activity = timezone.now()
            self.save()

    def end_session(self):
        """End the session"""
        indian_tz = self.get_indian_timezone()
        self.session_end = timezone.now().astimezone(indian_tz)
        self.status = 'completed'
        self.last_activity = timezone.now()
        self.save()

    def update_activity(self):
        """Update last activity timestamp (for heartbeat)"""
        self.last_activity = timezone.now()
        self.save(update_fields=['last_activity'])

    @classmethod
    def get_or_create_active_session(cls, student, document):
        """Get existing active session or create a new one"""
        # Check for existing active or paused session
        session = cls.objects.filter(
            student=student,
            document=document,
            status__in=['active', 'paused']
        ).first()

        if not session:
            # Create new session with proper initialization
            indian_tz = pytz.timezone('Asia/Kolkata')
            session = cls.objects.create(
                student=student,
                document=document,
                session_start=timezone.now().astimezone(indian_tz),
                status='active'
            )

        return session

    @classmethod
    def cleanup_abandoned_sessions(cls, timeout_minutes=30):
        """Mark sessions as abandoned if no activity for specified time"""
        cutoff_time = timezone.now() - timedelta(minutes=timeout_minutes)
        abandoned_count = cls.objects.filter(
            status__in=['active', 'paused'],
            last_activity__lt=cutoff_time
        ).update(status='abandoned')
        return abandoned_count


class DocumentTimeInterval(models.Model):
    """
    Model to track individual time intervals within a document session.
    This allows for precise tracking of active vs paused time.
    """
    INTERVAL_TYPE = [
        ('study', 'Study Time'),
        ('quiz_pause', 'Quiz Pause'),
        ('break', 'Break'),
        ('idle', 'Idle Time')
    ]

    session = models.ForeignKey(DocumentTimeSession, on_delete=models.CASCADE, related_name='intervals')
    interval_type = models.CharField(max_length=20, choices=INTERVAL_TYPE, default='study')
    start_time = models.DateTimeField(help_text="Start time of this interval (Indian timezone)")
    end_time = models.DateTimeField(null=True, blank=True, help_text="End time of this interval (Indian timezone)")
    duration_seconds = models.PositiveIntegerField(default=0, help_text="Duration of this interval in seconds")
    notes = models.TextField(blank=True, null=True, help_text="Additional notes about this interval")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        indexes = [
            models.Index(fields=['session', 'interval_type']),
            models.Index(fields=['start_time']),
            models.Index(fields=['interval_type']),
        ]
        ordering = ['start_time']
        verbose_name = "Document Time Interval"
        verbose_name_plural = "Document Time Intervals"

    def __str__(self):
        return f"{self.session.student.username} - {self.session.document.title} - {self.get_interval_type_display()} - {self.duration_seconds}s"

    @property
    def duration_formatted(self):
        """Return duration in HH:MM:SS format"""
        hours = self.duration_seconds // 3600
        minutes = (self.duration_seconds % 3600) // 60
        seconds = self.duration_seconds % 60
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"

    def get_indian_timezone(self):
        """Get Indian timezone object"""
        return pytz.timezone('Asia/Kolkata')

    def start_interval(self, interval_type='study', notes=None):
        """Start a new interval"""
        indian_tz = self.get_indian_timezone()
        self.interval_type = interval_type
        self.start_time = timezone.now().astimezone(indian_tz)
        self.notes = notes
        self.save()

    def end_interval(self):
        """End the current interval and calculate duration"""
        if self.start_time and not self.end_time:
            indian_tz = self.get_indian_timezone()
            self.end_time = timezone.now().astimezone(indian_tz)

            # Calculate duration
            duration = self.end_time - self.start_time
            self.duration_seconds = int(duration.total_seconds())

            # Update session total time if this is study time
            if self.interval_type == 'study':
                self.session.total_time_seconds += self.duration_seconds
                self.session.save(update_fields=['total_time_seconds'])

            self.save()

    @classmethod
    def create_study_interval(cls, session):
        """Create a new study interval for a session"""
        # End any existing active interval for this session
        active_interval = cls.objects.filter(
            session=session,
            end_time__isnull=True
        ).first()

        if active_interval:
            active_interval.end_interval()

        # Create new study interval
        interval = cls.objects.create(session=session)
        interval.start_interval('study')
        return interval

    @classmethod
    def create_quiz_pause_interval(cls, session, notes=None):
        """Create a quiz pause interval"""
        # End any existing active interval
        active_interval = cls.objects.filter(
            session=session,
            end_time__isnull=True
        ).first()

        if active_interval:
            active_interval.end_interval()

        # Create quiz pause interval
        interval = cls.objects.create(session=session)
        interval.start_interval('quiz_pause', notes)
        return interval


class DocumentTimeStats(models.Model):
    """
    Model to store aggregated time statistics per user per document.
    This provides quick access to summary statistics without complex queries.
    """
    student = models.ForeignKey('Student', on_delete=models.CASCADE, related_name='document_time_stats')
    document = models.ForeignKey('documents.Document', on_delete=models.CASCADE, related_name='time_stats')
    total_study_time_seconds = models.PositiveIntegerField(default=0, help_text="Total active study time in seconds")
    total_sessions = models.PositiveIntegerField(default=0, help_text="Total number of sessions")
    total_quiz_pauses = models.PositiveIntegerField(default=0, help_text="Number of times quiz was taken")
    average_session_duration = models.FloatField(default=0.0, help_text="Average session duration in seconds")
    first_access = models.DateTimeField(null=True, blank=True, help_text="First time document was accessed")
    last_access = models.DateTimeField(null=True, blank=True, help_text="Last time document was accessed")
    view_count = models.PositiveIntegerField(default=0, help_text="Number of times document was viewed")
    reopened_at_least_once = models.BooleanField(default=False, help_text="True if view_count > 1")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('student', 'document')
        indexes = [
            models.Index(fields=['student', 'document']),
            models.Index(fields=['total_study_time_seconds']),
            models.Index(fields=['view_count']),
            models.Index(fields=['last_access']),
        ]
        ordering = ['-last_access']
        verbose_name = "Document Time Statistics"
        verbose_name_plural = "Document Time Statistics"

    def __str__(self):
        return f"{self.student.username} - {self.document.title} - {self.total_study_time_formatted} - {self.total_sessions} sessions"

    @property
    def total_study_time_formatted(self):
        """Return total study time in HH:MM:SS format"""
        hours = self.total_study_time_seconds // 3600
        minutes = (self.total_study_time_seconds % 3600) // 60
        seconds = self.total_study_time_seconds % 60
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"

    @property
    def average_session_duration_formatted(self):
        """Return average session duration in HH:MM:SS format"""
        avg_seconds = int(self.average_session_duration)
        hours = avg_seconds // 3600
        minutes = (avg_seconds % 3600) // 60
        seconds = avg_seconds % 60
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"

    def get_indian_timezone(self):
        """Get Indian timezone object"""
        return pytz.timezone('Asia/Kolkata')

    def increment_view_count(self):
        """Increment view count and update reopened flag"""
        self.view_count += 1
        if self.view_count > 1:
            self.reopened_at_least_once = True

        # Update last access time
        indian_tz = self.get_indian_timezone()
        self.last_access = timezone.now().astimezone(indian_tz)

        # Set first access if not set
        if not self.first_access:
            self.first_access = self.last_access

        self.save()

    def update_from_session(self, session):
        """Update stats based on a completed session"""
        # Add study time from session intervals
        study_intervals = session.intervals.filter(interval_type='study')
        session_study_time = sum(interval.duration_seconds for interval in study_intervals)

        self.total_study_time_seconds += session_study_time
        self.total_sessions += 1

        # Count quiz pauses
        quiz_pauses = session.intervals.filter(interval_type='quiz_pause').count()
        self.total_quiz_pauses += quiz_pauses

        # Update average session duration
        if self.total_sessions > 0:
            self.average_session_duration = self.total_study_time_seconds / self.total_sessions

        # Update last access
        indian_tz = self.get_indian_timezone()
        self.last_access = timezone.now().astimezone(indian_tz)

        self.save()

    @classmethod
    def get_or_create_stats(cls, student, document):
        """Get or create stats record for student and document"""
        stats, created = cls.objects.get_or_create(
            student=student,
            document=document,
            defaults={
                'first_access': timezone.now().astimezone(pytz.timezone('Asia/Kolkata')),
                'last_access': timezone.now().astimezone(pytz.timezone('Asia/Kolkata')),
                'view_count': 1
            }
        )

        if not created:
            stats.increment_view_count()

        return stats
