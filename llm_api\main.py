from fastapi import FastAP<PERSON>, HTTPException, Depends, status, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from typing import Optional, List, Dict, Any
import os
import json
import re
import logging
import sys

# Add the current directory to the path so we can import local modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from dotenv import load_dotenv

import httpx
from preprocessing import preprocess_pdf, create_embeddings

# Import models - use absolute imports instead of relative
from models import (
    InferenceRequest, InferenceResponse,
    DocumentProcessResponse, BlueprintProcessRequest, BlueprintProcessResponse,
    BlueprintTopic, UserInfo, QuizGenerationRequest, QuizGenerationResponse, QuizQuestion,
    Flashcard, FlashcardGenerationRequest, FlashcardGenerationResponse,
    SummaryGenerationRequest, SummaryGenerationResponse,
    FlowchartGenerationRequest, FlowchartGenerationResponse,
    BlueprintGenerationRequest, BlueprintGenerationResponse
)
from auth import get_current_user
from llms import get_openai_response, get_gemini_response

import google.generativeai as genai

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)


# Load environment variables
load_dotenv()

# Get Django server URL from environment variables
DJANGO_SERVER_URL = os.getenv("DJANGO_SERVER_URL", "http://localhost:8000")

# Configure Gemini
GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY")
genai.configure(api_key=GOOGLE_API_KEY)
gen_model = genai.GenerativeModel(model_name="gemini-1.5-flash")

# Initialize FastAPI app
app = FastAPI(
    title="LLM Inference API",
    description="API for LLM inference with user authentication and usage tracking",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "http://localhost:8000",
        "http://127.0.0.1:8000",
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add middleware to log all requests
@app.middleware("http")
async def log_requests(request, call_next):
    """Log all incoming requests to the FastAPI server."""
    # Get request details
    request_id = id(request)
    client_host = request.client.host if request.client else "unknown"
    request_path = request.url.path
    request_method = request.method
    request_headers = dict(request.headers)

    # Log the request
    logging.info(f"Request {request_id} received: {request_method} {request_path} from {client_host}")
    logging.info(f"Request {request_id} headers: {request_headers}")

    # Process the request
    try:
        response = await call_next(request)
        logging.info(f"Request {request_id} completed with status code: {response.status_code}")
        return response
    except Exception as e:
        logging.error(f"Request {request_id} failed with error: {str(e)}")
        raise




@app.post("/infer", response_model=InferenceResponse)
async def infer(
    request: InferenceRequest,
    user: UserInfo = Depends(get_current_user)
):
    """
    Make an inference request to the specified LLM model.

    - **model**: The LLM to use ("openai" or "gemini")
    - **message**: The message/prompt for the model
    - **context**: Optional context to provide to the model

    Requires authentication via Token token.
    """
    model_name = request.model.lower()

    try:
        if model_name == "openai":
            response, tokens = await get_openai_response(request.message, request.context)
        elif model_name == "gemini":
            response, tokens = await get_gemini_response(request.message, request.context)
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Unsupported model. Please use 'openai' or 'gemini'"
            )

        # Get usage stats from Django server
        async with httpx.AsyncClient() as client:
            stats_response = await client.get(
                f"{DJANGO_SERVER_URL}/api/users/{user.id}/usage-stats/",
                headers={"Authorization": f"Token {user.token}"}  # Using user ID as a simple auth mechanism
            )

            if stats_response.status_code == 200:
                usage_stats = stats_response.json()
            else:
                usage_stats = None

        return InferenceResponse(
            response=response,
            model=model_name,
            tokens=tokens,
            usage_stats=usage_stats
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


@app.post("/process-document/{document_id}", response_model=DocumentProcessResponse)
async def process_document(
    document_id: int,
    file: UploadFile = File(...),
    user: UserInfo = Depends(get_current_user)
):
    """
    Process an uploaded document (PDF or text file):
    1. Extract and clean text from the document
    2. Create embeddings for text chunks
    3. Send embeddings to Django server for storage
    """
    try:
        # Read file content
        content = await file.read()

        # Log file information
        logging.info(f"Processing file: {file.filename}, size: {len(content)} bytes, content type: {file.content_type}")

        # Process document (PDF or text)
        try:
            clean_text = await preprocess_pdf(content)
        except Exception as e:
            logging.error(f"Error in document preprocessing: {str(e)}")
            raise ValueError(f"Failed to process document: {str(e)}")

        # Generate embeddings and text chunks
        text_chunks, embeddings = create_embeddings(clean_text)

        logging.info(f"Generated {len(text_chunks)} text chunks from document")

        # Create a list of text chunks with their embeddings
        chunks = []
        for idx, (chunk, emb) in enumerate(zip(text_chunks, embeddings)):
            chunks.append({
                "text": chunk,
                "embedding": emb,
                "chunk_number": idx
            })

        # Send the embeddings to Django server for storage
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{DJANGO_SERVER_URL}/api/documents/{document_id}/store-embeddings/",
                json={
                    "document_id": document_id,
                    "chunks": chunks
                },
                headers={"Authorization": f"Token {user.token}"}
            )

            if response.status_code != 200:
                error_msg = f"Failed to store embeddings in Django server: {response.text}"
                logging.error(error_msg)
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=error_msg
                )

        return DocumentProcessResponse(
            message="Document processed successfully",
            document_id=document_id,
            num_chunks=len(text_chunks),
            status="completed"
        )

    except ValueError as e:
        # Specific handling for preprocessing errors
        logging.error(f"Document preprocessing error: {str(e)}")

        # Notify Django server about the failure
        try:
            async with httpx.AsyncClient() as client:
                await client.post(
                    f"{DJANGO_SERVER_URL}/api/documents/{document_id}/update-status/",
                    json={
                        "status": "failed",
                        "error_message": str(e)
                    },
                    headers={"Authorization": f"Token {user.token}"}
                )
        except Exception as notify_error:
            logging.error(f"Failed to notify Django server: {str(notify_error)}")

        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Error processing document: {str(e)}"
        )
    except Exception as e:
        # General error handling
        logging.error(f"Unexpected error processing document: {str(e)}")

        # Notify Django server about the failure
        try:
            async with httpx.AsyncClient() as client:
                await client.post(
                    f"{DJANGO_SERVER_URL}/api/documents/{document_id}/update-status/",
                    json={
                        "status": "failed",
                        "error_message": str(e)
                    },
                    headers={"Authorization": f"Token {user.token}"}
                )
        except Exception as notify_error:
            logging.error(f"Failed to notify Django server: {str(notify_error)}")

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error processing document: {str(e)}"
        )


@app.post("/process-blueprint/{document_id}", response_model=BlueprintProcessResponse)
async def process_blueprint(
    document_id: int,
    blueprint_request: BlueprintProcessRequest,
    user: UserInfo = Depends(get_current_user)
):
    """
    Process a document blueprint:
    1. Use LLM to identify topics and their weightage
    2. Send topics to Django server for storage and linking with document embeddings

    Request body:
    - document_id: ID of the document to process
    - user_id: ID of the user making the request
    - blueprint_text: The blueprint text to analyze
    - llm_model: The LLM model to use ("openai" or "gemini")
    """
    try:
        # Use the specified LLM to identify topics and their weightage
        topics = await identify_topics_with_llm(
            blueprint_request.blueprint_text,
            user,
            blueprint_request.llm_model
        )

        # Convert topics to the expected format
        topic_list = [
            BlueprintTopic(title=topic['title'], weightage=topic['weightage'])
            for topic in topics
        ]

        # Send the topics to Django server for storage and linking
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{DJANGO_SERVER_URL}/api/documents/{document_id}/store-topics/",
                json={
                    "document_id": document_id,
                    "topics": [{"title": t.title, "weightage": t.weightage} for t in topic_list]
                },
                headers={"Authorization": f"Token {user.token}"}
            )

            if response.status_code != 200:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to store topics in Django server: {response.text}"
                )

        return BlueprintProcessResponse(
            message="Blueprint processed successfully",
            document_id=document_id,
            topics=topic_list,
            status="completed"
        )

    except Exception as e:
        # Notify Django server about the failure
        try:
            async with httpx.AsyncClient() as client:
                await client.post(
                    f"{DJANGO_SERVER_URL}/api/documents/{document_id}/update-status/",
                    json={
                        "status": "failed",
                        "error_message": str(e)
                    },
                    headers={"Authorization": f"Token {user.token}"}
                )
        except Exception:
            # If we can't notify Django, we still want to return an error to the client
            pass

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error processing blueprint: {str(e)}"
        )


async def identify_topics_with_llm(blueprint_text: str, user: UserInfo, llm_model: str = "openai") -> list:
    """
    Use LLM to identify topics and their weightage from blueprint text.
    Returns a list of dictionaries with 'title' and 'weightage' keys.
    Uses the existing infer endpoint for LLM requests.

    Parameters:
    - blueprint_text: The text to analyze
    - user: The user making the request
    - llm_model: The LLM model to use ("openai" or "gemini")
    """
    prompt = f"""
    Analyze the following document blueprint and identify the main topics covered.
    For each topic, assign a weightage (percentage importance) that reflects its significance in the document.
    The sum of all weightages should be 100%.

    Return the result as a JSON array of objects, each with 'title' and 'weightage' properties.
    Example: [{"title": "Introduction to AI", "weightage": 25.5}, {"title": "Machine Learning Basics", "weightage": 30.0}]

    Blueprint:
    {blueprint_text}
    """

    try:
        # Create a request to the infer endpoint
        request = InferenceRequest(
            model=llm_model,  # Use the specified model
            message=prompt,
            context="You are a document analysis assistant that identifies topics and their importance."
        )

        # Call the infer function directly
        response = await infer(request, user)

        # Extract the response text
        response_text = response.response

        # Extract and parse the JSON response
        import json
        import re

        # Find JSON array in the response
        json_match = re.search(r'\[.*\]', response_text, re.DOTALL)
        if json_match:
            json_str = json_match.group(0)
            topics = json.loads(json_str)
        else:
            # Fallback if no JSON array is found
            topics = [{"title": "General Content", "weightage": 100.0}]

        return topics
    except Exception as e:
        # Log the error for debugging
        import logging
        logging.error(f"Error in identify_topics_with_llm: {str(e)}")

        # Fallback in case of error
        return [{"title": "General Content", "weightage": 100.0}]


async def find_relevant_embeddings(topic: str, document_id: int, user: UserInfo) -> list:
    """
    Find embeddings relevant to a topic using semantic search.
    Makes a request to Django server to perform the search.

    Parameters:
    - topic: The topic to search for
    - document_id: The document ID to search in
    - user: The user making the request

    Returns:
    - List of embedding IDs that are relevant to the topic
    """
    try:
        # Make a request to Django server to find relevant embeddings
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{DJANGO_SERVER_URL}/api/documents/{document_id}/find-relevant-embeddings/",
                json={
                    "topic": topic,
                    "document_id": document_id
                },
                headers={"Authorization": f"Token {user.token}"}
            )

            if response.status_code != 200:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to find relevant embeddings: {response.text}"
                )

            return response.json()["embedding_ids"]
    except Exception as e:
        logging.error(f"Error in find_relevant_embeddings: {str(e)}")
        return []


@app.post("/generate-quiz/{document_id}", response_model=QuizGenerationResponse)
async def generate_quiz(
    document_id: int,
    quiz_request: QuizGenerationRequest = None,
    user: UserInfo = Depends(get_current_user)
):
    """
    Generate quiz questions and answers based on document content:
    1. Retrieve all document embeddings for the specified document
    2. Group the text from the document embeddings
    3. Use LLM to generate questions and answers based on the content
    4. Return the generated questions and answers

    - **document_id**: ID of the document to generate quiz for
    - **llm_model**: The LLM model to use ("openai" or "gemini")
    - **num_questions**: Number of questions to generate (default: 5)

    Requires authentication via Token token.
    """
    try:
        # If no request body is provided, create a default one
        if quiz_request is None:
            quiz_request = QuizGenerationRequest(document_id=document_id)

        # Ensure document_id in path matches the one in request body
        if quiz_request.document_id != document_id:
            quiz_request.document_id = document_id

        # Get all document embeddings from Django server
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{DJANGO_SERVER_URL}/api/documents/{document_id}/embeddings/",
                headers={"Authorization": f"Token {user.token}"}
            )

            if response.status_code != 200:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Failed to retrieve document embeddings: {response.text}"
                )

            embeddings_data = response.json()

        # Extract text from embeddings and group by chunks
        # Sort by chunk_number to maintain document order
        text_chunks = []
        for embedding in sorted(embeddings_data, key=lambda x: x.get('chunk_number', 0)):
            text_chunks.append(embedding.get('text_chunk', ''))

        # Combine text chunks into a single document
        # Limit to first 10 chunks to avoid token limits
        combined_text = " ".join(text_chunks[:10])

        # Generate quiz questions using LLM
        questions = await generate_quiz_questions(
            combined_text,
            user,
            quiz_request.llm_model,
            quiz_request.num_questions
        )

        return QuizGenerationResponse(
            message="Quiz generated successfully",
            document_id=document_id,
            questions=questions['questions'],
            status="completed",
            model=quiz_request.llm_model,
            tokens=questions['tokens']
        )

    except Exception as e:
        logging.error(f"Error generating quiz: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating quiz: {str(e)}"
        )


async def generate_quiz_questions(text: str, user: UserInfo, llm_model: str = "openai", num_questions: int = 5) -> dict:
    """
    Use LLM to generate quiz questions and answers from text.
    Returns a dictionary with 'questions' and 'tokens' keys.
    Uses the existing infer endpoint for LLM requests.

    Parameters:
    - text: The text to generate questions from
    - user: The user making the request
    - llm_model: The LLM model to use ("openai" or "gemini")
    - num_questions: Number of questions to generate
    """
    prompt = f"""
    Based on the following text, generate {num_questions} quiz questions and their answers.
    The questions should test understanding of key concepts in the text.

    Return the result as a JSON array of objects, each with 'question' and 'answer' properties.
    Example: [{{"question": "What is the capital of France?", "answer": "The capital of France is Paris."}}]

    Text:
    {text}
    """

    try:
        # Create a request to the infer endpoint
        request = InferenceRequest(
            model=llm_model,
            message=prompt,
            context="You are an educational quiz generator that creates insightful questions and clear answers."
        )

        # Call the infer function directly
        response = await infer(request, user)

        # Extract the response text
        response_text = response.response

        # Extract and parse the JSON response
        import json
        import re

        # Find JSON array in the response
        json_match = re.search(r'\[.*\]', response_text, re.DOTALL)
        if json_match:
            json_str = json_match.group(0)
            quiz_data = json.loads(json_str)

            # Convert to QuizQuestion objects
            questions = [
                QuizQuestion(question=item['question'], answer=item['answer'])
                for item in quiz_data
            ]
        else:
            # Fallback if no JSON array is found
            questions = [
                QuizQuestion(
                    question=f"Question {i+1} about the document content?",
                    answer=f"This is a fallback answer for question {i+1}."
                )
                for i in range(num_questions)
            ]

        return {
            "questions": questions,
            "tokens": response.tokens
        }
    except Exception as e:
        # Log the error for debugging
        logging.error(f"Error in generate_quiz_questions: {str(e)}")

        # Fallback in case of error
        fallback_questions = [
            QuizQuestion(
                question=f"Question {i+1} about the document content?",
                answer=f"This is a fallback answer for question {i+1}."
            )
            for i in range(num_questions)
        ]

        return {
            "questions": fallback_questions,
            "tokens": 0
        }


@app.get("/test-auth")
async def test_auth(user: UserInfo = Depends(get_current_user)):
    """
    Test authentication using the actual authentication mechanism from auth.py.

    This endpoint uses the same authentication flow as the other API endpoints,
    requiring a proper Authorization header with a Token token.

    Returns user information if authentication is successful.
    """
    return {
        "status": "success",
        "message": "Authentication successful",
        "user_info": {
            "id": user.id,
            "username": user.username,
            "email": user.email
        }
    }


@app.post("/generate-flashcards/{document_id}", response_model=FlashcardGenerationResponse)
async def generate_flashcards_endpoint(
    document_id: int,
    flashcard_request: FlashcardGenerationRequest = None,
    user: UserInfo = Depends(get_current_user)
):
    """
    Generate flashcards based on document content:
    1. Retrieve all document embeddings for the specified document
    2. Group the text from the document embeddings
    3. Use LLM to generate flashcards based on the content
    4. Return the generated flashcards

    - **document_id**: ID of the document to generate flashcards for
    - **llm_model**: The LLM model to use ("openai" or "gemini")
    - **num_flashcards**: Number of flashcards to generate (default: 10)

    Requires authentication via Bearer token.
    """
    try:
        num_flashcards = flashcard_request.num_flashcards if flashcard_request else 10
        context = await retrieve_context_for_document(document_id, user, top_k=5)
        prompt = f"""
        You are an expert tutor designing flashcards for effective learning.\nBased on the provided content, create {num_flashcards} flashcards.\n\nFlashcard Formatting Instructions:\n- Each flashcard must have a clear **Question** followed by its **Answer**.\n- Use the format:\n  Question: [Write the question here]\n  Answer: [Write the answer here]\n- Separate each flashcard with two newlines.\n\nContent:\n{context}\n\nCreate exactly {num_flashcards} flashcards in the specified format.
        """
        response = gen_model.generate_content(prompt)
        flashcards = []
        for flashcard in response.text.strip().split("\n\n"):
            parts = flashcard.split("\n")
            question_line = next((line for line in parts if line.startswith("Question:")), None)
            answer_line = next((line for line in parts if line.startswith("Answer:")), None)
            if question_line and answer_line:
                question = question_line.replace("Question:", "").strip()
                answer = answer_line.replace("Answer:", "").strip()
                flashcards.append(Flashcard(front=question, back=answer))
        return FlashcardGenerationResponse(
            message="Flashcards generated successfully",
            document_id=document_id,
            flashcards=flashcards,
            status="completed",
            model="gemini",
            tokens=0
        )
    except Exception as e:
        logging.error(f"Error generating flashcards: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating flashcards: {str(e)}"
        )


@app.post("/generate-flowchart/{document_id}", response_model=FlowchartGenerationResponse)
async def generate_flowchart_endpoint(
    document_id: int,
    flowchart_request: FlowchartGenerationRequest = None,
    user: UserInfo = Depends(get_current_user)
):
    """
    Generate a flowchart based on document content using RAG and Gemini.

    - **document_id**: ID of the document to generate flowchart for
    - **llm_model**: The LLM model to use ("openai" or "gemini")

    Requires authentication via Bearer token.
    """
    try:
        llm_model = flowchart_request.llm_model if flowchart_request else "gemini"
        context = await retrieve_context_for_document(document_id, user, top_k=5)

        prompt = f"""
        You are an expert at creating educational flowcharts using Mermaid syntax.

        Based on the content below, create a comprehensive flowchart that:
        1. Shows the main concepts and their relationships
        2. Uses proper Mermaid syntax (flowchart TD or LR)
        3. Includes decision points, processes, and connections
        4. Is educational and easy to understand

        Content:
        {context}

        Output only the Mermaid code for the flowchart. Start with 'flowchart TD' or 'flowchart LR'.
        """

        response = gen_model.generate_content(prompt)
        mermaid_code = response.text.strip()

        # Estimate tokens
        tokens = len(prompt.split()) + len(mermaid_code.split())

        return FlowchartGenerationResponse(
            message="Flowchart generated successfully",
            document_id=document_id,
            flowchart=mermaid_code,
            status="completed",
            model=llm_model,
            tokens=tokens
        )
    except Exception as e:
        logging.error(f"Error generating flowchart: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating flowchart: {str(e)}"
        )


@app.post("/generate-summary/{document_id}", response_model=SummaryGenerationResponse)
async def generate_summary_endpoint(
    document_id: int,
    summary_request: SummaryGenerationRequest = None,
    user: UserInfo = Depends(get_current_user)
):
    """
    Generate a summary based on document content using RAG and Gemini.

    - **document_id**: ID of the document to generate summary for
    - **llm_model**: The LLM model to use ("openai" or "gemini")
    - **summary_type**: Type of summary ("comprehensive", "brief", "key_points")

    Requires authentication via Bearer token.
    """
    try:
        llm_model = summary_request.llm_model if summary_request else "gemini"
        summary_type = summary_request.summary_type if summary_request else "comprehensive"

        context = await retrieve_context_for_document(document_id, user, top_k=8)

        # Create different prompts based on summary type
        if summary_type == "brief":
            prompt = f"""
            You are an expert at creating concise summaries.

            Based on the content below, create a brief summary that:
            1. Captures the main points in 2-3 paragraphs
            2. Uses clear, accessible language
            3. Focuses on the most important concepts

            Content:
            {context}

            Provide a brief summary in markdown format.
            """
        elif summary_type == "key_points":
            prompt = f"""
            You are an expert at extracting key information.

            Based on the content below, create a key points summary that:
            1. Lists the main concepts as bullet points
            2. Organizes information hierarchically
            3. Uses clear headings and subheadings
            4. Highlights the most important takeaways

            Content:
            {context}

            Provide key points in markdown format with proper headings and bullet points.
            """
        else:  # comprehensive
            prompt = f"""
            You are an expert at creating comprehensive summaries.

            Based on the content below, create a detailed summary that:
            1. Covers all major topics and concepts
            2. Explains relationships between ideas
            3. Provides context and background information
            4. Uses proper headings and structure
            5. Includes examples where relevant

            Content:
            {context}

            Provide a comprehensive summary in markdown format with proper headings, subheadings, and formatting.
            """

        response = gen_model.generate_content(prompt)
        summary_text = response.text.strip()

        # Estimate tokens
        tokens = len(prompt.split()) + len(summary_text.split())

        return SummaryGenerationResponse(
            message="Summary generated successfully",
            document_id=document_id,
            summary=summary_text,
            status="completed",
            model=llm_model,
            tokens=tokens
        )
    except Exception as e:
        logging.error(f"Error generating summary: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating summary: {str(e)}"
        )


@app.post("/generate-blueprint/{document_id}", response_model=BlueprintGenerationResponse)
async def generate_blueprint_endpoint(
    document_id: int,
    blueprint_request: BlueprintGenerationRequest = None,
    user: UserInfo = Depends(get_current_user)
):
    """
    Generate a blueprint based on document content using RAG and Gemini.

    A Blueprint helps Cognimosity understand which topics or areas you want to prioritize.
    This will influence how content is analyzed, summarized, and presented across all features.

    - **document_id**: ID of the document to generate blueprint for
    - **llm_model**: The LLM model to use ("openai" or "gemini")
    - **focus_areas**: Optional focus areas to emphasize in the blueprint

    Requires authentication via Bearer token.
    """
    try:
        llm_model = blueprint_request.llm_model if blueprint_request else "gemini"
        focus_areas = blueprint_request.focus_areas if blueprint_request else None

        context = await retrieve_context_for_document(document_id, user, top_k=8)

        # Create prompt for blueprint generation
        focus_instruction = f"\nSpecial focus on: {focus_areas}" if focus_areas else ""

        prompt = f"""
        You are an expert educational content analyzer and learning strategist.

        Based on the content below, create a comprehensive learning blueprint that:

        1. **Identifies Key Topics**: Extract and prioritize the main topics and concepts
        2. **Assigns Weightage**: Give each topic a weightage (0-100) based on importance and complexity
        3. **Creates Learning Path**: Suggest a logical sequence for learning these topics
        4. **Provides Context**: Explain why each topic is important and how it relates to others

        Format your response as a detailed blueprint in markdown with:
        - **Overview**: Brief summary of the document's scope
        - **Learning Objectives**: What learners should achieve
        - **Topic Breakdown**: Detailed analysis of each major topic with weightage
        - **Recommended Learning Sequence**: Suggested order of study
        - **Key Connections**: How topics relate to each other

        Also provide a JSON array of topics with their weightages at the end in this format:
        ```json
        [
          {{"title": "Topic Name", "weightage": 85.0}},
          {{"title": "Another Topic", "weightage": 70.0}}
        ]
        ```

        Content:
        {context}
        {focus_instruction}

        Provide a comprehensive blueprint in markdown format followed by the JSON array.
        """

        response = gen_model.generate_content(prompt)
        blueprint_text = response.text.strip()

        # Extract JSON array from the response
        import json
        import re

        # Find JSON array in the response
        json_match = re.search(r'```json\s*(\[.*?\])\s*```', blueprint_text, re.DOTALL)
        topics = []
        if json_match:
            try:
                json_str = json_match.group(1)
                topics_data = json.loads(json_str)
                topics = [BlueprintTopic(title=topic["title"], weightage=topic["weightage"]) for topic in topics_data]
            except (json.JSONDecodeError, KeyError) as e:
                logging.warning(f"Failed to parse topics JSON: {e}")
                # Fallback topics
                topics = [BlueprintTopic(title="General Content", weightage=100.0)]
        else:
            # Fallback if no JSON array is found
            topics = [BlueprintTopic(title="General Content", weightage=100.0)]

        # Estimate tokens
        tokens = len(prompt.split()) + len(blueprint_text.split())

        return BlueprintGenerationResponse(
            message="Blueprint generated successfully",
            document_id=document_id,
            blueprint=blueprint_text,
            topics=topics,
            status="completed",
            model=llm_model,
            tokens=tokens
        )
    except Exception as e:
        logging.error(f"Error generating blueprint: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating blueprint: {str(e)}"
        )





# Helper: Retrieve top paragraphs for a document using embeddings
async def retrieve_context_for_document(document_id: int, user: UserInfo, top_k: int = 3):
    async with httpx.AsyncClient() as client:
        response = await client.get(
            f"{DJANGO_SERVER_URL}/api/documents/{document_id}/embeddings/",
            headers={"Authorization": f"Token {user.token}"}
        )
        if response.status_code != 200:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to retrieve document embeddings: {response.text}"
            )
        embeddings_data = response.json()
    # Sort and get text chunks
    text_chunks = [e.get('text_chunk', '') for e in sorted(embeddings_data, key=lambda x: x.get('chunk_number', 0))]
    # For now, just join the top_k chunks (could use similarity search if needed)
    return "\n\n".join(text_chunks[:top_k])


@app.get("/test-django-connection")
async def test_django_connection():
    """
    Test the connection to the Django server.
    """
    try:
        # First, try a public endpoint that doesn't require authentication
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{DJANGO_SERVER_URL}/admin/login/")

            if response.status_code == 200:
                return {"status": "success", "message": "Connection to Django server successful (public endpoint)"}
            else:
                return {
                    "status": "error",
                    "message": f"Connection to Django server failed with status code {response.status_code} (public endpoint)",
                    "response": response.text[:100]  # Limit response text to first 100 chars
                }
    except Exception as e:
        return {"status": "error", "message": f"Connection to Django server failed: {str(e)}"}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
