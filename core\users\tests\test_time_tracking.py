from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from rest_framework.test import APIClient
from rest_framework.authtoken.models import Token
from users.models import Student, DocumentTimeSession, DocumentTimeInterval, DocumentTimeStats
from documents.models import Document
import tempfile
import os


class TimeTrackingAPITestCase(TestCase):
    def setUp(self):
        # Create test user
        self.user = Student.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create auth token
        self.token = Token.objects.create(user=self.user)
        
        # Create test document
        # Create a temporary file for testing
        self.temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.txt')
        self.temp_file.write(b'Test document content')
        self.temp_file.close()
        
        with open(self.temp_file.name, 'rb') as f:
            self.document = Document.objects.create(
                user=self.user,
                title='Test Document',
                file=f.name
            )
        
        # Set up API client
        self.client = APIClient()
        self.client.credentials(HTTP_AUTHORIZATION=f'Token {self.token.key}')
    
    def tearDown(self):
        # Clean up temporary file
        if os.path.exists(self.temp_file.name):
            os.unlink(self.temp_file.name)
    
    def test_start_session(self):
        """Test starting a document session"""
        url = reverse('start-document-session')
        data = {'document_id': self.document.id}

        response = self.client.post(url, data, format='json')

        if response.status_code != 200:
            print(f"Response status: {response.status_code}")
            print(f"Response data: {response.data}")

        self.assertEqual(response.status_code, 200)
        self.assertIn('session', response.data)
        self.assertIn('message', response.data)

        # Check session was created
        session = DocumentTimeSession.objects.get(id=response.data['session']['id'])
        self.assertEqual(session.student, self.user)
        self.assertEqual(session.document, self.document)
        self.assertEqual(session.status, 'active')
    
    def test_update_activity(self):
        """Test updating session activity"""
        # First create a session
        session = DocumentTimeSession.get_or_create_active_session(self.user, self.document)
        
        url = reverse('update-session-activity')
        data = {'session_id': session.id}
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('message', response.data)
        self.assertIn('last_activity', response.data)
    
    def test_pause_resume_session(self):
        """Test pausing and resuming a session"""
        # Create a session
        session = DocumentTimeSession.get_or_create_active_session(self.user, self.document)
        
        url = reverse('pause-resume-session')
        
        # Test pause
        data = {'session_id': session.id, 'action': 'pause', 'notes': 'Taking quiz'}
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, 200)
        session.refresh_from_db()
        self.assertEqual(session.status, 'paused')
        
        # Test resume
        data = {'session_id': session.id, 'action': 'resume'}
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, 200)
        session.refresh_from_db()
        self.assertEqual(session.status, 'active')
    
    def test_end_session(self):
        """Test ending a session"""
        # Create a session
        session = DocumentTimeSession.get_or_create_active_session(self.user, self.document)
        
        url = reverse('end-document-session')
        data = {'session_id': session.id}
        
        response = self.client.post(url, data, format='json')
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('session', response.data)
        self.assertIn('stats', response.data)
        
        session.refresh_from_db()
        self.assertEqual(session.status, 'completed')
    
    def test_get_document_stats(self):
        """Test getting document statistics"""
        # Create some stats
        stats = DocumentTimeStats.get_or_create_stats(self.user, self.document)
        
        url = reverse('get-document-time-stats', kwargs={'document_id': self.document.id})
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('stats', response.data)
        self.assertIn('recent_sessions', response.data)
    
    def test_get_user_overview(self):
        """Test getting user time tracking overview"""
        # Create some stats
        DocumentTimeStats.get_or_create_stats(self.user, self.document)
        
        url = reverse('get-user-time-overview')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, 200)
        self.assertIn('overview', response.data)
        self.assertIn('document_stats', response.data)
        self.assertIn('active_sessions', response.data)
    
    def test_unauthorized_access(self):
        """Test that unauthorized users cannot access endpoints"""
        # Remove authentication
        self.client.credentials()
        
        url = reverse('start-document-session')
        data = {'document_id': self.document.id}
        
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, 401)
    
    def test_document_ownership(self):
        """Test that users can only track time for their own documents"""
        # Create another user and document
        other_user = Student.objects.create_user(
            username='otheruser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        with open(self.temp_file.name, 'rb') as f:
            other_document = Document.objects.create(
                user=other_user,
                title='Other Document',
                file=f.name
            )
        
        url = reverse('start-document-session')
        data = {'document_id': other_document.id}
        
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, 404)  # Should not find document
