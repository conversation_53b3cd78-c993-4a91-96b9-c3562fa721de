"use client"

import React, { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Skeleton } from "@/components/ui/skeleton"
import { Copy, CheckCircle, FileText, Sparkles, RefreshCw, Target } from "lucide-react"
import { toast } from "sonner"
import { blueprintApi } from "@/lib/api"
import { Markdown } from "@/components/ui/markdown"

interface Topic {
  title: string;
  weightage: number;
}

interface BlueprintInterfaceProps {
  documentId?: number;
}

export function BlueprintInterface({ documentId }: BlueprintInterfaceProps) {
  const [blueprint, setBlueprint] = useState<string>("")
  const [topics, setTopics] = useState<Topic[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isGenerating, setIsGenerating] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [copied, setCopied] = useState(false)
  const [focusAreas, setFocusAreas] = useState("")

  useEffect(() => {
    if (documentId) {
      loadBlueprint()
    }
  }, [documentId])

  const loadBlueprint = async () => {
    if (!documentId) {
      setError("No document selected")
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      // First try to get existing blueprint
      try {
        const existingBlueprint = await blueprintApi.getBlueprint(documentId)
        if (existingBlueprint && existingBlueprint.blueprint) {
          setBlueprint(existingBlueprint.blueprint)
          setTopics(existingBlueprint.topics || [])
          return
        }
      } catch (existingError) {
        console.log('No existing blueprint found, will generate new one')
      }

      // If no existing blueprint, generate new one
      console.log('Generating new blueprint for document:', documentId)
      const response = await blueprintApi.generateBlueprint(documentId, focusAreas)
      setBlueprint(response.blueprint)
      setTopics(response.topics || [])
    } catch (err) {
      console.error('Error loading blueprint:', err)
      setError('Failed to load blueprint. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const generateNewBlueprint = async () => {
    if (!documentId) {
      toast.error("No document selected for blueprint generation")
      return
    }

    setIsGenerating(true)
    setError(null)

    try {
      console.log('Generating new blueprint for document:', documentId, 'focus areas:', focusAreas)
      const response = await blueprintApi.generateBlueprint(documentId, focusAreas)
      setBlueprint(response.blueprint)
      setTopics(response.topics || [])
      toast.success("New blueprint generated successfully!")
    } catch (err) {
      console.error('Error generating blueprint:', err)
      setError('Failed to generate new blueprint. Please try again.')
      toast.error("Failed to generate new blueprint")
    } finally {
      setIsGenerating(false)
    }
  }

  const copyToClipboard = async () => {
    if (!blueprint) return

    try {
      await navigator.clipboard.writeText(blueprint)
      setCopied(true)
      toast.success("Blueprint copied to clipboard!")

      // Reset copied state after 2 seconds
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Failed to copy:', err)
      toast.error("Failed to copy blueprint")
    }
  }

  if (!documentId) {
    return (
      <div className="p-6 space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Learning Blueprint
            </CardTitle>
            <CardDescription>
              Select a document to view or generate its learning blueprint
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-lg p-4 bg-purple-600/10 border border-purple-600/30">
              <h3 className="font-medium text-purple-500 mb-2">What is a Blueprint?</h3>
              <p className="text-sm text-neutral-300">
                A Blueprint helps Cognimosity understand which topics or areas you want to prioritize.
                This will influence how content is analyzed, summarized, and presented across all features.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="p-6 space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Learning Blueprint
            </CardTitle>
            <CardDescription>
              {blueprint ? "Generating new blueprint..." : "Loading blueprint..."}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-2/3" />
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6 space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Learning Blueprint
            </CardTitle>
            <CardDescription className="text-red-500">
              {error}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={loadBlueprint} className="w-full">
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Learning Blueprint
          </CardTitle>
          <CardDescription>
            AI-generated learning blueprint for your document content
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Focus Areas Input */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Focus Areas (Optional):</label>
            <Textarea
              value={focusAreas}
              onChange={(e) => setFocusAreas(e.target.value)}
              placeholder="Specify topics or areas you want to emphasize (e.g., machine learning, data science, algorithms)..."
              className="min-h-[60px] bg-neutral-900 border-neutral-700"
            />
          </div>

          {/* Topics Overview */}
          {topics.length > 0 && (
            <div className="space-y-2">
              <h3 className="text-sm font-medium">Key Topics Identified:</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                {topics.map((topic, index) => (
                  <div key={index} className="flex items-center justify-between p-2 bg-neutral-800 rounded">
                    <span className="text-sm">{topic.title}</span>
                    <span className="text-xs text-purple-400 font-medium">{topic.weightage.toFixed(0)}%</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Blueprint Content */}
          {blueprint && (
            <div className="border rounded-lg p-4 bg-neutral-900/50">
              <Markdown content={blueprint} />
            </div>
          )}

          <div className="flex gap-2 pt-4 border-t">
            <Button
              onClick={copyToClipboard}
              variant="outline"
              className="flex-1"
              disabled={!blueprint}
            >
              {copied ? (
                <>
                  <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                  Copied!
                </>
              ) : (
                <>
                  <Copy className="h-4 w-4 mr-2" />
                  Copy Blueprint
                </>
              )}
            </Button>
            <Button
              onClick={loadBlueprint}
              variant="outline"
              className="flex-1"
              disabled={isLoading || isGenerating}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Reload
            </Button>
            <Button
              onClick={generateNewBlueprint}
              className="flex-1"
              disabled={isLoading || isGenerating}
            >
              <Sparkles className="h-4 w-4 mr-2" />
              {isGenerating ? "Generating..." : "Generate New"}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}