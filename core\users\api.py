from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, authentication_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.authentication import TokenAuthentication
from .authentication import BearerTokenAuthentication
from .utils import get_usage_stats
from .models import DocumentTimeSession, DocumentTimeStats
from .serializers import (
    DocumentTimeSessionSerializer, DocumentTimeStatsSerializer
)
from django.shortcuts import get_object_or_404
from documents.models import Document

@api_view(['GET'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication, BearerTokenAuthentication])
def validate_token(request):
    """
    Validate the authentication token and return user information.
    Accepts both Token and Bearer authentication formats.
    """
    user = request.user

    # Log successful token validation for debugging
    import logging
    logger = logging.getLogger(__name__)
    auth_header = request.META.get('HTTP_AUTHORIZATION', '')
    logger.info(f"Token validation successful for user: {user.username}, auth header: {auth_header[:15]}...")

    return Response({
        "id": user.id,
        "username": user.username,
        "email": user.email
    })

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_user_usage_stats(request, user_id):
    """
    Get usage statistics for a user
    """
    # Ensure the requesting user can only access their own stats
    if int(user_id) != request.user.id:
        return Response(
            {"error": "You can only access your own usage statistics"},
            status=status.HTTP_403_FORBIDDEN
        )

    try:
        stats = get_usage_stats(request.user)
        return Response(stats)
    except Exception as e:
        return Response(
            {"error": f"Error getting usage statistics: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([AllowAny])
def test_auth(request):
    """
    Test authentication between Django and FastAPI servers.

    This endpoint:
    1. Collects information about the current request authentication
    2. Makes a request to the FastAPI test-auth endpoint using the current user's token
    3. Returns both the local authentication info and the FastAPI response

    This helps diagnose authentication issues between the two servers.
    """
    import requests
    from django.conf import settings

    # Get the authentication header
    auth_header = request.META.get('HTTP_AUTHORIZATION', '')

    # Check if the user is authenticated
    is_authenticated = request.user.is_authenticated

    # Prepare the response with local auth info
    response_data = {
        "django_auth_info": {
            "auth_header": {
                "received": bool(auth_header),
                "value": auth_header[:10] + "..." if auth_header and len(auth_header) > 10 else auth_header,
                "format": "Unknown"
            },
            "authentication": {
                "is_authenticated": is_authenticated,
                "auth_method": str(request.auth.__class__.__name__) if request.auth else "None"
            }
        },
        "fastapi_test_results": None
    }

    # Add format information
    if auth_header:
        if auth_header.lower().startswith('token '):
            response_data["django_auth_info"]["auth_header"]["format"] = "Token"
        elif auth_header.lower().startswith('bearer '):
            response_data["django_auth_info"]["auth_header"]["format"] = "Bearer"
        else:
            response_data["django_auth_info"]["auth_header"]["format"] = "Raw (no prefix)"

    # Add user information if authenticated
    if is_authenticated:
        response_data["django_auth_info"]["user"] = {
            "id": request.user.id,
            "username": request.user.username,
            "email": request.user.email
        }

        # Get the token for the current user
        from rest_framework.authtoken.models import Token
        try:
            token, _ = Token.objects.get_or_create(user=request.user)
            token_key = token.key

            # Now test this token with the FastAPI server
            fastapi_url = getattr(settings, 'FASTAPI_URL', 'http://localhost:8001')

            # Test with different auth header formats
            auth_formats = [
                {"name": "Bearer format", "header": f"Bearer {token_key}"},
                {"name": "Token format", "header": f"Token {token_key}"},
                {"name": "Raw token", "header": token_key}
            ]

            fastapi_results = {}

            for auth_format in auth_formats:
                try:
                    # Make request to FastAPI test-auth endpoint
                    fastapi_response = requests.get(
                        f"{fastapi_url}/test-auth",
                        headers={"Authorization": auth_format["header"]},
                        timeout=5.0
                    )

                    if fastapi_response.status_code == 200:
                        fastapi_results[auth_format["name"]] = {
                            "status": "success",
                            "status_code": fastapi_response.status_code,
                            "response": fastapi_response.json()
                        }
                    else:
                        fastapi_results[auth_format["name"]] = {
                            "status": "error",
                            "status_code": fastapi_response.status_code,
                            "response": fastapi_response.text[:100] if fastapi_response.text else "No response body"
                        }
                except Exception as e:
                    fastapi_results[auth_format["name"]] = {
                        "status": "exception",
                        "error": str(e)
                    }

            response_data["fastapi_test_results"] = {
                "token_used": f"{token_key[:5]}...",
                "fastapi_url": fastapi_url,
                "results": fastapi_results
            }

        except Exception as e:
            response_data["fastapi_test_results"] = {
                "status": "error",
                "message": f"Error testing with FastAPI: {str(e)}"
            }

    return Response(response_data)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def logout_view(request):
    """
    Logout endpoint that deletes the user's auth token.
    Compatible with frontend API calls to /users/logout/
    """
    try:
        # Delete the user's token
        from rest_framework.authtoken.models import Token
        try:
            token = Token.objects.get(user=request.user)
            token.delete()
        except Token.DoesNotExist:
            pass  # Token doesn't exist, which is fine

        return Response({
            "message": "Successfully logged out"
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            "error": f"Error during logout: {str(e)}"
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# Simple Platform Time Tracking API

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication, BearerTokenAuthentication])
def start_document_session(request):
    """
    Start or resume a document session when entering the process page.
    Pauses all other active sessions and starts/resumes session for this document.
    """
    try:
        document_id = request.data.get('document_id')
        if not document_id:
            return Response({
                'error': 'document_id is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        document = get_object_or_404(Document, id=document_id, user=request.user)

        # Pause all other active sessions for this user
        DocumentTimeSession.pause_all_active_sessions(request.user)

        # Start or resume session for this document
        session = DocumentTimeSession.start_or_resume_session(request.user, document)

        return Response({
            'session_id': session.id,
            'message': 'Document session started/resumed successfully',
            'is_resumed': session.total_time_seconds > 0,
            'total_time_seconds': session.total_time_seconds
        }, status=status.HTTP_201_CREATED)

    except Exception as e:
        return Response({
            'error': f'Error starting session: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication, BearerTokenAuthentication])
def pause_document_session(request):
    """
    Pause a document session when leaving the process page or switching documents.
    """
    try:
        document_id = request.data.get('document_id')

        if document_id:
            # Pause specific document session
            document = get_object_or_404(Document, id=document_id, user=request.user)
            session = DocumentTimeSession.get_active_session(request.user, document)

            if session:
                session.pause_session()

                # Update stats
                stats = DocumentTimeStats.get_or_create_stats(request.user, document)
                stats.update_from_session(session)

                return Response({
                    'message': 'Document session paused successfully',
                    'total_time_seconds': session.total_time_seconds
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    'message': 'No active session found for this document'
                }, status=status.HTTP_404_NOT_FOUND)
        else:
            # Pause all active sessions for user (when going to home page)
            DocumentTimeSession.pause_all_active_sessions(request.user)
            return Response({
                'message': 'All active sessions paused successfully'
            }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'error': f'Error pausing session: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication, BearerTokenAuthentication])
def get_document_stats(request):
    """
    Get document-specific time statistics for the user.
    """
    try:
        document_id = request.GET.get('document_id')

        if document_id:
            # Get stats for specific document
            document = get_object_or_404(Document, id=document_id, user=request.user)
            stats = DocumentTimeStats.get_or_create_stats(request.user, document)

            # Get recent sessions for this document
            recent_sessions = DocumentTimeSession.objects.filter(
                student=request.user,
                document=document
            ).order_by('-session_start')[:5]

            session_data = []
            for session in recent_sessions:
                session_data.append({
                    'id': session.id,
                    'start_time': session.session_start,
                    'end_time': session.session_end,
                    'total_time_seconds': session.total_time_seconds,
                    'is_active': session.is_active
                })

            return Response({
                'document_stats': {
                    'document_id': document.id,
                    'document_title': document.title,
                    'total_time_seconds': stats.total_time_seconds,
                    'total_sessions': stats.total_sessions,
                    'view_count': stats.view_count,
                    'was_reopened': stats.was_reopened,
                    'average_session_time': stats.average_session_time,
                    'first_access': stats.first_access,
                    'last_access': stats.last_access
                },
                'recent_sessions': session_data
            }, status=status.HTTP_200_OK)
        else:
            # Get overview stats for all documents
            all_stats = DocumentTimeStats.objects.filter(student=request.user)

            # Calculate totals
            total_time = sum(stat.total_time_seconds for stat in all_stats)
            total_sessions = sum(stat.total_sessions for stat in all_stats)
            total_documents = all_stats.count()

            # Get document breakdown
            document_breakdown = []
            for stat in all_stats.order_by('-total_time_seconds')[:10]:
                document_breakdown.append({
                    'document_id': stat.document.id,
                    'document_title': stat.document.title,
                    'total_time_seconds': stat.total_time_seconds,
                    'total_sessions': stat.total_sessions,
                    'view_count': stat.view_count,
                    'was_reopened': stat.was_reopened
                })

            return Response({
                'overview': {
                    'total_time_seconds': total_time,
                    'total_sessions': total_sessions,
                    'total_documents': total_documents,
                },
                'document_breakdown': document_breakdown
            }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'error': f'Error getting stats: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


