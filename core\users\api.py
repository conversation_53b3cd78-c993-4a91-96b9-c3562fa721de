from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, authentication_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.authentication import TokenAuthentication
from .authentication import BearerTokenAuthentication
from .utils import get_usage_stats
from .models import DocumentTimeSession, DocumentTimeInterval, DocumentTimeStats
from .serializers import (
    DocumentTimeSessionSerializer, DocumentTimeIntervalSerializer,
    DocumentTimeStatsSerializer, StartSessionSerializer, UpdateActivitySerializer,
    PauseResumeSessionSerializer, EndSessionSerializer
)
from django.shortcuts import get_object_or_404
from documents.models import Document

@api_view(['GET'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication, BearerTokenAuthentication])
def validate_token(request):
    """
    Validate the authentication token and return user information.
    Accepts both Token and Bearer authentication formats.
    """
    user = request.user

    # Log successful token validation for debugging
    import logging
    logger = logging.getLogger(__name__)
    auth_header = request.META.get('HTTP_AUTHORIZATION', '')
    logger.info(f"Token validation successful for user: {user.username}, auth header: {auth_header[:15]}...")

    return Response({
        "id": user.id,
        "username": user.username,
        "email": user.email
    })

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_user_usage_stats(request, user_id):
    """
    Get usage statistics for a user
    """
    # Ensure the requesting user can only access their own stats
    if int(user_id) != request.user.id:
        return Response(
            {"error": "You can only access your own usage statistics"},
            status=status.HTTP_403_FORBIDDEN
        )

    try:
        stats = get_usage_stats(request.user)
        return Response(stats)
    except Exception as e:
        return Response(
            {"error": f"Error getting usage statistics: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([AllowAny])
def test_auth(request):
    """
    Test authentication between Django and FastAPI servers.

    This endpoint:
    1. Collects information about the current request authentication
    2. Makes a request to the FastAPI test-auth endpoint using the current user's token
    3. Returns both the local authentication info and the FastAPI response

    This helps diagnose authentication issues between the two servers.
    """
    import requests
    from django.conf import settings

    # Get the authentication header
    auth_header = request.META.get('HTTP_AUTHORIZATION', '')

    # Check if the user is authenticated
    is_authenticated = request.user.is_authenticated

    # Prepare the response with local auth info
    response_data = {
        "django_auth_info": {
            "auth_header": {
                "received": bool(auth_header),
                "value": auth_header[:10] + "..." if auth_header and len(auth_header) > 10 else auth_header,
                "format": "Unknown"
            },
            "authentication": {
                "is_authenticated": is_authenticated,
                "auth_method": str(request.auth.__class__.__name__) if request.auth else "None"
            }
        },
        "fastapi_test_results": None
    }

    # Add format information
    if auth_header:
        if auth_header.lower().startswith('token '):
            response_data["django_auth_info"]["auth_header"]["format"] = "Token"
        elif auth_header.lower().startswith('bearer '):
            response_data["django_auth_info"]["auth_header"]["format"] = "Bearer"
        else:
            response_data["django_auth_info"]["auth_header"]["format"] = "Raw (no prefix)"

    # Add user information if authenticated
    if is_authenticated:
        response_data["django_auth_info"]["user"] = {
            "id": request.user.id,
            "username": request.user.username,
            "email": request.user.email
        }

        # Get the token for the current user
        from rest_framework.authtoken.models import Token
        try:
            token, _ = Token.objects.get_or_create(user=request.user)
            token_key = token.key

            # Now test this token with the FastAPI server
            fastapi_url = getattr(settings, 'FASTAPI_URL', 'http://localhost:8001')

            # Test with different auth header formats
            auth_formats = [
                {"name": "Bearer format", "header": f"Bearer {token_key}"},
                {"name": "Token format", "header": f"Token {token_key}"},
                {"name": "Raw token", "header": token_key}
            ]

            fastapi_results = {}

            for auth_format in auth_formats:
                try:
                    # Make request to FastAPI test-auth endpoint
                    fastapi_response = requests.get(
                        f"{fastapi_url}/test-auth",
                        headers={"Authorization": auth_format["header"]},
                        timeout=5.0
                    )

                    if fastapi_response.status_code == 200:
                        fastapi_results[auth_format["name"]] = {
                            "status": "success",
                            "status_code": fastapi_response.status_code,
                            "response": fastapi_response.json()
                        }
                    else:
                        fastapi_results[auth_format["name"]] = {
                            "status": "error",
                            "status_code": fastapi_response.status_code,
                            "response": fastapi_response.text[:100] if fastapi_response.text else "No response body"
                        }
                except Exception as e:
                    fastapi_results[auth_format["name"]] = {
                        "status": "exception",
                        "error": str(e)
                    }

            response_data["fastapi_test_results"] = {
                "token_used": f"{token_key[:5]}...",
                "fastapi_url": fastapi_url,
                "results": fastapi_results
            }

        except Exception as e:
            response_data["fastapi_test_results"] = {
                "status": "error",
                "message": f"Error testing with FastAPI: {str(e)}"
            }

    return Response(response_data)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def logout_view(request):
    """
    Logout endpoint that deletes the user's auth token.
    Compatible with frontend API calls to /users/logout/
    """
    try:
        # Delete the user's token
        from rest_framework.authtoken.models import Token
        try:
            token = Token.objects.get(user=request.user)
            token.delete()
        except Token.DoesNotExist:
            pass  # Token doesn't exist, which is fine

        return Response({
            "message": "Successfully logged out"
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            "error": f"Error during logout: {str(e)}"
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# Time Tracking API Endpoints

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication, BearerTokenAuthentication])
def start_document_session(request):
    """
    Start a new document time tracking session or get existing active session.
    """
    serializer = StartSessionSerializer(data=request.data)
    if not serializer.is_valid():
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    document_id = serializer.validated_data['document_id']

    try:
        # Verify document exists and belongs to user
        document = get_object_or_404(Document, id=document_id, user=request.user)

        # Get or create active session
        session = DocumentTimeSession.get_or_create_active_session(request.user, document)

        # Create or get stats record and increment view count
        stats = DocumentTimeStats.get_or_create_stats(request.user, document)

        # Create initial study interval if session is new
        if not session.intervals.exists():
            DocumentTimeInterval.create_study_interval(session)

        session_serializer = DocumentTimeSessionSerializer(session)
        return Response({
            'session': session_serializer.data,
            'message': 'Session started successfully'
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'error': f'Error starting session: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication, BearerTokenAuthentication])
def update_session_activity(request):
    """
    Update session activity (heartbeat) to track user presence.
    """
    serializer = UpdateActivitySerializer(data=request.data)
    if not serializer.is_valid():
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    session_id = serializer.validated_data['session_id']

    try:
        # Verify session exists and belongs to user
        session = get_object_or_404(DocumentTimeSession, id=session_id, student=request.user)

        # Update activity timestamp
        session.update_activity()

        return Response({
            'message': 'Activity updated successfully',
            'last_activity': session.last_activity
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'error': f'Error updating activity: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication, BearerTokenAuthentication])
def pause_resume_session(request):
    """
    Pause or resume a document session (e.g., when starting/ending a quiz).
    """
    serializer = PauseResumeSessionSerializer(data=request.data)
    if not serializer.is_valid():
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    session_id = serializer.validated_data['session_id']
    action = serializer.validated_data['action']
    notes = serializer.validated_data.get('notes', '')

    try:
        # Verify session exists and belongs to user
        session = get_object_or_404(DocumentTimeSession, id=session_id, student=request.user)

        if action == 'pause':
            session.pause_session()
            # Create quiz pause interval
            if notes:
                DocumentTimeInterval.create_quiz_pause_interval(session, notes)
            message = 'Session paused successfully'
        else:  # resume
            session.resume_session()
            # Create new study interval
            DocumentTimeInterval.create_study_interval(session)
            message = 'Session resumed successfully'

        session_serializer = DocumentTimeSessionSerializer(session)
        return Response({
            'session': session_serializer.data,
            'message': message
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'error': f'Error {action}ing session: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication, BearerTokenAuthentication])
def end_document_session(request):
    """
    End a document session and update statistics.
    """
    serializer = EndSessionSerializer(data=request.data)
    if not serializer.is_valid():
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    session_id = serializer.validated_data['session_id']

    try:
        # Verify session exists and belongs to user
        session = get_object_or_404(DocumentTimeSession, id=session_id, student=request.user)

        # End any active intervals
        active_interval = session.intervals.filter(end_time__isnull=True).first()
        if active_interval:
            active_interval.end_interval()

        # End the session
        session.end_session()

        # Update statistics
        stats = DocumentTimeStats.get_or_create_stats(request.user, session.document)
        stats.update_from_session(session)

        session_serializer = DocumentTimeSessionSerializer(session)
        stats_serializer = DocumentTimeStatsSerializer(stats)

        return Response({
            'session': session_serializer.data,
            'stats': stats_serializer.data,
            'message': 'Session ended successfully'
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'error': f'Error ending session: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication, BearerTokenAuthentication])
def get_document_time_stats(request, document_id):
    """
    Get time tracking statistics for a specific document.
    """
    try:
        # Verify document exists and belongs to user
        document = get_object_or_404(Document, id=document_id, user=request.user)

        # Get stats
        try:
            stats = DocumentTimeStats.objects.get(student=request.user, document=document)
            stats_serializer = DocumentTimeStatsSerializer(stats)

            # Get recent sessions
            recent_sessions = DocumentTimeSession.objects.filter(
                student=request.user,
                document=document
            ).order_by('-session_start')[:5]
            sessions_serializer = DocumentTimeSessionSerializer(recent_sessions, many=True)

            return Response({
                'stats': stats_serializer.data,
                'recent_sessions': sessions_serializer.data
            }, status=status.HTTP_200_OK)

        except DocumentTimeStats.DoesNotExist:
            return Response({
                'stats': None,
                'recent_sessions': [],
                'message': 'No time tracking data found for this document'
            }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'error': f'Error getting stats: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication, BearerTokenAuthentication])
def get_user_time_overview(request):
    """
    Get overview of user's time tracking across all documents.
    """
    try:
        # Get all stats for user
        all_stats = DocumentTimeStats.objects.filter(student=request.user).order_by('-last_access')
        stats_serializer = DocumentTimeStatsSerializer(all_stats, many=True)

        # Calculate totals
        total_study_time = sum(stat.total_study_time_seconds for stat in all_stats)
        total_sessions = sum(stat.total_sessions for stat in all_stats)
        total_documents = all_stats.count()

        # Get active sessions
        active_sessions = DocumentTimeSession.objects.filter(
            student=request.user,
            status__in=['active', 'paused']
        )
        active_sessions_serializer = DocumentTimeSessionSerializer(active_sessions, many=True)

        return Response({
            'overview': {
                'total_study_time_seconds': total_study_time,
                'total_sessions': total_sessions,
                'total_documents': total_documents,
                'total_study_time_formatted': f"{total_study_time // 3600:02d}:{(total_study_time % 3600) // 60:02d}:{total_study_time % 60:02d}"
            },
            'document_stats': stats_serializer.data,
            'active_sessions': active_sessions_serializer.data
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'error': f'Error getting overview: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication, BearerTokenAuthentication])
def cleanup_abandoned_sessions(request):
    """
    Cleanup abandoned sessions (admin/maintenance endpoint).
    """
    try:
        timeout_minutes = request.data.get('timeout_minutes', 30)
        abandoned_count = DocumentTimeSession.cleanup_abandoned_sessions(timeout_minutes)

        return Response({
            'message': f'Cleaned up {abandoned_count} abandoned sessions',
            'abandoned_count': abandoned_count
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'error': f'Error cleaning up sessions: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
